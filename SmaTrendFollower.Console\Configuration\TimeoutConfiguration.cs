using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Configuration;

/// <summary>
/// Centralized timeout configuration for all system components
/// </summary>
public class TimeoutConfiguration
{
    public HttpTimeouts Http { get; set; } = new();
    public WebSocketTimeouts WebSocket { get; set; } = new();
    public MarketDataTimeouts MarketData { get; set; } = new();
    public TradingTimeouts Trading { get; set; } = new();
    public DatabaseTimeouts Database { get; set; } = new();
    public RetryTimeouts Retry { get; set; } = new();
}

public class HttpTimeouts
{
    /// <summary>
    /// Standard HTTP request timeout for API calls
    /// </summary>
    public TimeSpan StandardRequest { get; set; } = TimeSpan.FromSeconds(45);
    
    /// <summary>
    /// Quick operations like account status, market status
    /// </summary>
    public TimeSpan QuickOperation { get; set; } = TimeSpan.FromSeconds(15);
    
    /// <summary>
    /// Long operations like large data downloads
    /// </summary>
    public TimeSpan LongOperation { get; set; } = TimeSpan.FromMinutes(2);
    
    /// <summary>
    /// Connection timeout for establishing HTTP connections
    /// </summary>
    public TimeSpan Connection { get; set; } = TimeSpan.FromSeconds(10);
    
    /// <summary>
    /// Polly timeout policy (should be less than StandardRequest)
    /// </summary>
    public TimeSpan PollyTimeout { get; set; } = TimeSpan.FromSeconds(40);
}

public class WebSocketTimeouts
{
    /// <summary>
    /// WebSocket connection establishment timeout
    /// </summary>
    public TimeSpan Connection { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// Heartbeat interval for keeping connections alive
    /// </summary>
    public TimeSpan Heartbeat { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// Maximum time to wait for a WebSocket message
    /// </summary>
    public TimeSpan MessageReceive { get; set; } = TimeSpan.FromMinutes(2);
    
    /// <summary>
    /// Timeout for sending WebSocket messages
    /// </summary>
    public TimeSpan MessageSend { get; set; } = TimeSpan.FromSeconds(10);
    
    /// <summary>
    /// Base delay between reconnection attempts
    /// </summary>
    public TimeSpan ReconnectBaseDelay { get; set; } = TimeSpan.FromSeconds(2);
    
    /// <summary>
    /// Maximum delay between reconnection attempts
    /// </summary>
    public TimeSpan ReconnectMaxDelay { get; set; } = TimeSpan.FromMinutes(2);
}

public class MarketDataTimeouts
{
    /// <summary>
    /// Timeout for fetching stock bars
    /// </summary>
    public TimeSpan StockBars { get; set; } = TimeSpan.FromMinutes(1);
    
    /// <summary>
    /// Timeout for fetching index data
    /// </summary>
    public TimeSpan IndexData { get; set; } = TimeSpan.FromSeconds(45);
    
    /// <summary>
    /// Timeout for VIX data retrieval - increased to 60 seconds to handle Polygon API latency
    /// </summary>
    public TimeSpan VixData { get; set; } = TimeSpan.FromSeconds(60);
    
    /// <summary>
    /// Timeout for parallel market data fetching operations - increased to handle large symbol universes
    /// </summary>
    public TimeSpan ParallelFetch { get; set; } = TimeSpan.FromMinutes(5);
    
    /// <summary>
    /// Timeout for real-time streaming data
    /// </summary>
    public TimeSpan StreamingData { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Timeout for individual symbol data requests
    /// </summary>
    public TimeSpan SymbolTimeout { get; set; } = TimeSpan.FromSeconds(30);
}

public class TradingTimeouts
{
    /// <summary>
    /// Timeout for trade execution
    /// </summary>
    public TimeSpan TradeExecution { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// Timeout for order placement
    /// </summary>
    public TimeSpan OrderPlacement { get; set; } = TimeSpan.FromSeconds(20);
    
    /// <summary>
    /// Timeout for order cancellation
    /// </summary>
    public TimeSpan OrderCancellation { get; set; } = TimeSpan.FromSeconds(15);
    
    /// <summary>
    /// Timeout for portfolio operations
    /// </summary>
    public TimeSpan PortfolioOperations { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// Timeout for signal generation - reduced to prevent hangs
    /// </summary>
    public TimeSpan SignalGeneration { get; set; } = TimeSpan.FromSeconds(90);
}

public class DatabaseTimeouts
{
    /// <summary>
    /// Timeout for Redis operations
    /// </summary>
    public TimeSpan Redis { get; set; } = TimeSpan.FromSeconds(10);
    
    /// <summary>
    /// Timeout for SQLite operations
    /// </summary>
    public TimeSpan SQLite { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// Timeout for cache warming operations
    /// </summary>
    public TimeSpan CacheWarming { get; set; } = TimeSpan.FromMinutes(5);
}

public class RetryTimeouts
{
    /// <summary>
    /// Base delay for retry operations
    /// </summary>
    public TimeSpan BaseDelay { get; set; } = TimeSpan.FromSeconds(1);
    
    /// <summary>
    /// Maximum delay for retry operations
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// Maximum total time for retry operations
    /// </summary>
    public TimeSpan MaxTotalTime { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// Extension methods for timeout configuration
/// </summary>
public static class TimeoutConfigurationExtensions
{
    /// <summary>
    /// Configures timeout settings from configuration
    /// </summary>
    public static TimeoutConfiguration ConfigureTimeouts(this IConfiguration configuration)
    {
        var timeouts = new TimeoutConfiguration();
        configuration.GetSection("Timeouts").Bind(timeouts);
        
        // Apply environment-specific overrides
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
        
        if (environment == "Development")
        {
            // Longer timeouts for development/debugging to handle large symbol universes
            timeouts.Http.StandardRequest = TimeSpan.FromMinutes(1);
            timeouts.MarketData.ParallelFetch = TimeSpan.FromMinutes(10);
            timeouts.Trading.SignalGeneration = TimeSpan.FromMinutes(5);
        }
        else if (environment == "Testing")
        {
            // Shorter timeouts for testing
            timeouts.Http.StandardRequest = TimeSpan.FromSeconds(15);
            timeouts.MarketData.ParallelFetch = TimeSpan.FromSeconds(30);
            timeouts.Trading.SignalGeneration = TimeSpan.FromSeconds(30);
        }
        
        return timeouts;
    }
    
    /// <summary>
    /// Validates timeout configuration
    /// </summary>
    public static void ValidateTimeouts(this TimeoutConfiguration timeouts, ILogger logger)
    {
        // Ensure Polly timeout is less than HTTP timeout
        if (timeouts.Http.PollyTimeout >= timeouts.Http.StandardRequest)
        {
            logger.LogWarning("Polly timeout ({PollyTimeout}) should be less than HTTP timeout ({HttpTimeout}). Adjusting automatically.",
                timeouts.Http.PollyTimeout, timeouts.Http.StandardRequest);
            timeouts.Http.PollyTimeout = timeouts.Http.StandardRequest.Subtract(TimeSpan.FromSeconds(5));
        }
        
        // Ensure connection timeout is reasonable
        if (timeouts.Http.Connection > TimeSpan.FromSeconds(30))
        {
            logger.LogWarning("HTTP connection timeout ({ConnectionTimeout}) is very high. Consider reducing it.",
                timeouts.Http.Connection);
        }
        
        // Ensure WebSocket timeouts are reasonable
        if (timeouts.WebSocket.Connection > TimeSpan.FromMinutes(1))
        {
            logger.LogWarning("WebSocket connection timeout ({WebSocketTimeout}) is very high. Consider reducing it.",
                timeouts.WebSocket.Connection);
        }
        
        logger.LogInformation("Timeout configuration validated successfully");
    }
}
