using Microsoft.Extensions.Logging;
using <PERSON>;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Rate limiting helper for Finnhub API with dual constraints:
/// - 30 API calls per second
/// - 60 API calls per minute
/// Plus plan-specific limits
/// </summary>
public sealed class FinnhubRateLimitHelper : IDisposable
{
    private readonly ILogger<FinnhubRateLimitHelper> _logger;
    private readonly SemaphoreSlim _semaphore = new(5, 5); // Allow 5 concurrent requests for 30 req/sec limit
    private readonly Timer _secondTimer;
    private readonly Timer _minuteTimer;
    private readonly IAsyncPolicy _retryPolicy;

    // Dual rate limiting counters
    private volatile int _requestsThisSecond = 0;
    private volatile int _requestsThisMinute = 0;
    
    // Conservative limits (slightly below actual limits for safety)
    private const int MaxRequestsPerSecond = 25;  // 25 instead of 30 for safety margin
    private const int MaxRequestsPerMinute = 55;  // 55 instead of 60 for safety margin
    
    private bool _disposed = false;

    public FinnhubRateLimitHelper(ILogger<FinnhubRateLimitHelper> logger)
    {
        _logger = logger;
        
        // Reset second counter every second
        _secondTimer = new Timer(ResetSecondCounter, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
        
        // Reset minute counter every minute
        _minuteTimer = new Timer(ResetMinuteCounter, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        // Exponential back-off retry policy for rate limit exceptions
        _retryPolicy = Policy
            .Handle<HttpRequestException>(ex => 
                ex.Message.Contains("429") || 
                ex.Message.Contains("TooManyRequests") ||
                ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => 
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + 
                    TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (exception, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Finnhub rate limit retry {RetryCount}/5 after {Delay}ms. Exception: {Exception}",
                        retryCount, timespan.TotalMilliseconds, exception.Message);
                });
    }

    /// <summary>
    /// Executes a Finnhub API call with dual rate limiting (30/sec AND 60/min) and retry logic
    /// </summary>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment both counters
            Interlocked.Increment(ref _requestsThisSecond);
            Interlocked.Increment(ref _requestsThisMinute);

            _logger.LogDebug("Executing Finnhub API call: {Operation} (Requests: {SecondCount}/25 per second, {MinuteCount}/55 per minute)",
                operationName, _requestsThisSecond, _requestsThisMinute);

            // Execute with retry policy
            return await _retryPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    return await apiCall();
                }
                catch (Exception ex) when (IsRateLimitException(ex))
                {
                    _logger.LogWarning("Finnhub rate limit hit for {Operation}, will retry with exponential back-off", operationName);
                    throw;
                }
            });
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Executes a Finnhub API call without return value with rate limiting and retry logic
    /// </summary>
    public async Task ExecuteAsync(Func<Task> apiCall, string operationName = "Unknown")
    {
        await ExecuteAsync(async () =>
        {
            await apiCall();
            return true; // Dummy return value
        }, operationName);
    }

    private async Task WaitIfNecessary()
    {
        // Check second limit first (more restrictive in short term)
        if (_requestsThisSecond >= MaxRequestsPerSecond)
        {
            var waitTime = TimeSpan.FromMilliseconds(1100); // Wait just over 1 second
            _logger.LogInformation("Finnhub second rate limit reached ({Count}/{Max}), waiting {Wait}ms", 
                _requestsThisSecond, MaxRequestsPerSecond, waitTime.TotalMilliseconds);
            await Task.Delay(waitTime);
        }
        
        // Check minute limit (more restrictive in long term)
        if (_requestsThisMinute >= MaxRequestsPerMinute)
        {
            var waitTime = TimeSpan.FromSeconds(65); // Wait just over 1 minute
            _logger.LogWarning("Finnhub minute rate limit reached ({Count}/{Max}), waiting {Wait}s", 
                _requestsThisMinute, MaxRequestsPerMinute, waitTime.TotalSeconds);
            await Task.Delay(waitTime);
        }

        // Add small delay when approaching limits to prevent bursts
        if (_requestsThisSecond >= MaxRequestsPerSecond * 0.8 || _requestsThisMinute >= MaxRequestsPerMinute * 0.8)
        {
            var delay = TimeSpan.FromMilliseconds(200 + Random.Shared.Next(0, 300));
            _logger.LogDebug("Approaching Finnhub rate limits, adding {Delay}ms delay", delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
    }

    private static bool IsRateLimitException(Exception ex)
    {
        return ex is HttpRequestException httpEx && (
            httpEx.Message.Contains("429") ||
            httpEx.Message.Contains("TooManyRequests") ||
            httpEx.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase)
        );
    }

    private void ResetSecondCounter(object? state)
    {
        var previousCount = Interlocked.Exchange(ref _requestsThisSecond, 0);
        if (previousCount > 0)
        {
            _logger.LogDebug("Reset Finnhub second counter (was {Count})", previousCount);
        }
    }

    private void ResetMinuteCounter(object? state)
    {
        var previousCount = Interlocked.Exchange(ref _requestsThisMinute, 0);
        if (previousCount > 0)
        {
            _logger.LogDebug("Reset Finnhub minute counter (was {Count})", previousCount);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _secondTimer?.Dispose();
            _minuteTimer?.Dispose();
            _semaphore?.Dispose();
            _disposed = true;
        }
    }
}
