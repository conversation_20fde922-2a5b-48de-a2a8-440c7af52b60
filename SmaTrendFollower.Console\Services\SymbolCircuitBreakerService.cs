using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Net;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Intelligent circuit breaker service that distinguishes between infrastructure and data failures.
/// Only circuit breaks for actual data issues, never for network/infrastructure problems.
/// Automatically resets each trading day to ensure no missed opportunities.
/// </summary>
public class IntelligentSymbolCircuitBreakerService
{
    private readonly ILogger<IntelligentSymbolCircuitBreakerService> _logger;
    private readonly ConcurrentDictionary<string, SymbolFailureInfo> _symbolFailures = new();
    private DateTime _lastTradingDayReset = DateTime.UtcNow.Date;

    public IntelligentSymbolCircuitBreakerService(ILogger<IntelligentSymbolCircuitBreakerService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Check if a symbol should be skipped due to circuit breaker.
    /// Only skips for actual data issues, never for infrastructure problems.
    /// </summary>
    public bool ShouldSkipSymbol(string symbol)
    {
        // Reset circuit breakers for new trading day
        CheckAndResetForNewTradingDay();

        if (!_symbolFailures.TryGetValue(symbol, out var failureInfo))
            return false;

        // Check if circuit is open and still within the open duration
        if (failureInfo.IsCircuitOpen &&
            DateTime.UtcNow - failureInfo.CircuitOpenedAt < failureInfo.CircuitDuration)
        {
            return true;
        }

        // Circuit has been open long enough, allow retry and reset if successful
        if (failureInfo.IsCircuitOpen &&
            DateTime.UtcNow - failureInfo.CircuitOpenedAt >= failureInfo.CircuitDuration)
        {
            _logger.LogInformation("Circuit breaker for {Symbol} is ready for retry after {Duration}",
                symbol, failureInfo.CircuitDuration);
            failureInfo.IsCircuitOpen = false;
        }

        return false;
    }

    /// <summary>
    /// Reset circuit breakers for new trading day to ensure no missed opportunities
    /// </summary>
    private void CheckAndResetForNewTradingDay()
    {
        var today = DateTime.UtcNow.Date;
        if (today > _lastTradingDayReset)
        {
            var resetCount = _symbolFailures.Count;
            _symbolFailures.Clear();
            _lastTradingDayReset = today;

            if (resetCount > 0)
            {
                _logger.LogInformation("Reset {Count} circuit breakers for new trading day", resetCount);
            }
        }
    }

    /// <summary>
    /// Record a failure for a symbol with intelligent error classification
    /// </summary>
    public void RecordFailure(string symbol, TimeSpan duration, string errorMessage, Exception? exception = null)
    {
        var errorType = ClassifyError(errorMessage, exception);
        var circuitDuration = GetCircuitDurationForError(errorType);

        // Only circuit break for actual data issues, not infrastructure problems
        if (errorType == ErrorType.Infrastructure)
        {
            _logger.LogWarning("Infrastructure failure for {Symbol}: {Error}. Duration: {Duration}ms. " +
                             "Will retry aggressively (no circuit break)",
                symbol, errorMessage, duration.TotalMilliseconds);
            return;
        }

        var failureInfo = _symbolFailures.AddOrUpdate(symbol,
            new SymbolFailureInfo
            {
                FailureCount = 1,
                LastFailure = DateTime.UtcNow,
                LastDuration = duration,
                LastError = errorMessage,
                ErrorType = errorType,
                CircuitDuration = circuitDuration,
                IsCircuitOpen = circuitDuration > TimeSpan.Zero,
                CircuitOpenedAt = circuitDuration > TimeSpan.Zero ? DateTime.UtcNow : DateTime.MinValue
            },
            (key, existing) => new SymbolFailureInfo
            {
                FailureCount = existing.FailureCount + 1,
                LastFailure = DateTime.UtcNow,
                LastDuration = duration,
                LastError = errorMessage,
                ErrorType = errorType,
                CircuitDuration = circuitDuration,
                IsCircuitOpen = circuitDuration > TimeSpan.Zero,
                CircuitOpenedAt = circuitDuration > TimeSpan.Zero ? DateTime.UtcNow : existing.CircuitOpenedAt
            });

        if (failureInfo.IsCircuitOpen)
        {
            _logger.LogWarning("Circuit breaker opened for {Symbol} ({ErrorType}) for {Duration}. " +
                             "Error: {Error}. Failure count: {FailureCount}",
                symbol, errorType, circuitDuration, errorMessage, failureInfo.FailureCount);
        }
        else
        {
            _logger.LogDebug("Failure recorded for {Symbol} ({ErrorType}): {Error}. " +
                           "Duration: {Duration}ms. Count: {FailureCount}",
                symbol, errorType, errorMessage, duration.TotalMilliseconds, failureInfo.FailureCount);
        }
    }

    /// <summary>
    /// Classify error type to determine appropriate response
    /// </summary>
    private ErrorType ClassifyError(string errorMessage, Exception? exception)
    {
        var message = errorMessage?.ToLowerInvariant() ?? "";

        // Data/Symbol issues - circuit break temporarily
        if (message.Contains("not found") || message.Contains("404") ||
            message.Contains("invalid symbol") || message.Contains("delisted"))
            return ErrorType.DataNotFound;

        if (message.Contains("unauthorized") || message.Contains("403") ||
            message.Contains("suspended") || message.Contains("forbidden"))
            return ErrorType.DataUnauthorized;

        if (message.Contains("invalid data") || message.Contains("malformed") ||
            message.Contains("parse error") || message.Contains("format"))
            return ErrorType.DataFormat;

        // Infrastructure issues - retry aggressively, no circuit break
        if (message.Contains("timeout") || message.Contains("slow response") ||
            message.Contains("network") || message.Contains("connection") ||
            message.Contains("socket") || message.Contains("dns"))
            return ErrorType.Infrastructure;

        if (message.Contains("rate limit") || message.Contains("429") ||
            message.Contains("throttle") || message.Contains("quota"))
            return ErrorType.Infrastructure;

        if (message.Contains("server error") || message.Contains("500") ||
            message.Contains("502") || message.Contains("503") || message.Contains("504"))
            return ErrorType.Infrastructure;

        // Default to infrastructure (be conservative - prefer retrying)
        return ErrorType.Infrastructure;
    }

    /// <summary>
    /// Get circuit breaker duration based on error type
    /// </summary>
    private TimeSpan GetCircuitDurationForError(ErrorType errorType)
    {
        return errorType switch
        {
            ErrorType.Infrastructure => TimeSpan.Zero,           // No circuit break - retry aggressively
            ErrorType.DataFormat => TimeSpan.FromMinutes(30),    // Medium break for data format issues
            ErrorType.DataNotFound => TimeSpan.FromHours(2),     // Longer break for missing symbols
            ErrorType.DataUnauthorized => TimeSpan.FromHours(4), // Longest break for auth issues
            _ => TimeSpan.FromMinutes(15)                        // Default fallback
        };
    }

    /// <summary>
    /// Record a successful operation for a symbol
    /// </summary>
    public void RecordSuccess(string symbol)
    {
        if (_symbolFailures.TryRemove(symbol, out var failureInfo))
        {
            _logger.LogInformation("Symbol {Symbol} recovered after {FailureCount} failures ({ErrorType})",
                symbol, failureInfo.FailureCount, failureInfo.ErrorType);
        }
    }

    /// <summary>
    /// Get current circuit breaker status for monitoring
    /// </summary>
    public Dictionary<string, SymbolFailureInfo> GetCircuitBreakerStatus()
    {
        return _symbolFailures.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// Manually reset circuit breaker for a symbol
    /// </summary>
    public void ResetCircuitBreaker(string symbol)
    {
        if (_symbolFailures.TryRemove(symbol, out var failureInfo))
        {
            _logger.LogInformation("Manually reset circuit breaker for {Symbol} " +
                                 "(was at {FailureCount} failures)", symbol, failureInfo.FailureCount);
        }
    }
}

public class SymbolFailureInfo
{
    public int FailureCount { get; set; }
    public DateTime LastFailure { get; set; }
    public TimeSpan LastDuration { get; set; }
    public string LastError { get; set; } = string.Empty;
    public ErrorType ErrorType { get; set; }
    public TimeSpan CircuitDuration { get; set; }
    public bool IsCircuitOpen { get; set; }
    public DateTime CircuitOpenedAt { get; set; }
}

public enum ErrorType
{
    Infrastructure,     // Network, timeouts, rate limits - retry aggressively
    DataNotFound,      // Symbol not found, 404 - circuit break for hours
    DataUnauthorized,  // Auth issues, suspended - circuit break for hours
    DataFormat         // Parse errors, malformed data - circuit break briefly
}
