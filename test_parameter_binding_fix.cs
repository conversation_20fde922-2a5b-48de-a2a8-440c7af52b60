using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Services;
using Serilog;
using System;
using System.Threading.Tasks;

/// <summary>
/// Test script to verify the parameter binding fix for database queries
/// This tests the enhanced GetCachedBarsAsync method with various parameter combinations
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();

        Console.WriteLine("=== Parameter Binding Fix Test ===");
        Console.WriteLine("Testing enhanced GetCachedBarsAsync method...\n");

        // Build host with database services
        var host = Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // Add database services
                services.AddSingleton<DatabaseConfigurationService>();
                services.AddDbContextFactory<StockBarCacheDbContext>((serviceProvider, options) =>
                {
                    var dbConfig = serviceProvider.GetRequiredService<DatabaseConfigurationService>();
                    var connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING") 
                        ?? "Host=localhost;Database=smatrendfollower;Username=postgres;Password=your_password";
                    
                    dbConfig.ConfigurePostgreSQL(options, connectionString);
                });
            })
            .Build();

        using var scope = host.Services.CreateScope();
        var contextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<StockBarCacheDbContext>>();

        try
        {
            Console.WriteLine("=== Test 1: Valid Parameters ===");
            await TestValidParameters(contextFactory);

            Console.WriteLine("\n=== Test 2: Invalid Parameters ===");
            await TestInvalidParameters(contextFactory);

            Console.WriteLine("\n=== Test 3: Edge Case Parameters ===");
            await TestEdgeCaseParameters(contextFactory);

            Console.WriteLine("\n=== Test 4: Parameter Sanitization ===");
            await TestParameterSanitization(contextFactory);

            Console.WriteLine("\n✅ All tests completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            await host.StopAsync();
            Log.CloseAndFlush();
        }
    }

    static async Task TestValidParameters(IDbContextFactory<StockBarCacheDbContext> contextFactory)
    {
        using var context = await contextFactory.CreateDbContextAsync();
        
        var symbol = "AAPL";
        var timeFrame = "Day";
        var startDate = DateTime.UtcNow.Date.AddDays(-30);
        var endDate = DateTime.UtcNow.Date.AddDays(-1);

        Console.WriteLine($"Testing: Symbol='{symbol}', TimeFrame='{timeFrame}', StartDate='{startDate:yyyy-MM-dd}', EndDate='{endDate:yyyy-MM-dd}'");
        
        var result = await context.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);
        Console.WriteLine($"✅ Result: {result.Count} bars retrieved");
    }

    static async Task TestInvalidParameters(IDbContextFactory<StockBarCacheDbContext> contextFactory)
    {
        using var context = await contextFactory.CreateDbContextAsync();

        // Test null symbol
        Console.WriteLine("Testing null symbol...");
        var result1 = await context.GetCachedBarsAsync(null, "Day", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ Null symbol handled: {result1.Count} bars (expected 0)");

        // Test empty timeframe
        Console.WriteLine("Testing empty timeframe...");
        var result2 = await context.GetCachedBarsAsync("AAPL", "", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ Empty timeframe handled: {result2.Count} bars (expected 0)");

        // Test invalid date range
        Console.WriteLine("Testing invalid date range...");
        var result3 = await context.GetCachedBarsAsync("AAPL", "Day", DateTime.UtcNow, DateTime.UtcNow.AddDays(-1));
        Console.WriteLine($"✅ Invalid date range handled: {result3.Count} bars (expected 0)");
    }

    static async Task TestEdgeCaseParameters(IDbContextFactory<StockBarCacheDbContext> contextFactory)
    {
        using var context = await contextFactory.CreateDbContextAsync();

        // Test very long symbol
        Console.WriteLine("Testing very long symbol...");
        var longSymbol = new string('A', 25); // Longer than 20 character limit
        var result1 = await context.GetCachedBarsAsync(longSymbol, "Day", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ Long symbol handled: {result1.Count} bars (expected 0)");

        // Test invalid timeframe
        Console.WriteLine("Testing invalid timeframe...");
        var result2 = await context.GetCachedBarsAsync("AAPL", "InvalidTimeFrame", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ Invalid timeframe handled: {result2.Count} bars (expected 0)");

        // Test extremely large date range
        Console.WriteLine("Testing extremely large date range...");
        var result3 = await context.GetCachedBarsAsync("AAPL", "Day", DateTime.UtcNow.AddYears(-20), DateTime.UtcNow);
        Console.WriteLine($"✅ Large date range handled: {result3.Count} bars (expected 0)");
    }

    static async Task TestParameterSanitization(IDbContextFactory<StockBarCacheDbContext> contextFactory)
    {
        using var context = await contextFactory.CreateDbContextAsync();

        // Test symbol with special characters
        Console.WriteLine("Testing symbol with special characters...");
        var result1 = await context.GetCachedBarsAsync("AAPL'; DROP TABLE CachedStockBars; --", "Day", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ SQL injection attempt handled: {result1.Count} bars (expected 0)");

        // Test lowercase symbol (should be sanitized to uppercase)
        Console.WriteLine("Testing lowercase symbol...");
        var result2 = await context.GetCachedBarsAsync("aapl", "Day", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ Lowercase symbol handled: {result2.Count} bars");

        // Test symbol with whitespace
        Console.WriteLine("Testing symbol with whitespace...");
        var result3 = await context.GetCachedBarsAsync("  AAPL  ", "Day", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Console.WriteLine($"✅ Symbol with whitespace handled: {result3.Count} bars");
    }
}
