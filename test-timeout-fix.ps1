#!/usr/bin/env pwsh

# Test script to verify timeout fixes are working
Write-Host "Testing timeout configuration fixes..." -ForegroundColor Green

# Check if the timeout configuration file has been updated
$timeoutConfig = Get-Content "SmaTrendFollower.Console/appsettings.timeouts.json" | ConvertFrom-Json
Write-Host "Current timeout configurations:" -ForegroundColor Yellow
Write-Host "  HTTP StandardRequest: $($timeoutConfig.Timeouts.Http.StandardRequest)" -ForegroundColor Cyan
Write-Host "  MarketData StockBars: $($timeoutConfig.Timeouts.MarketData.StockBars)" -ForegroundColor Cyan
Write-Host "  MarketData SymbolTimeout: $($timeoutConfig.Timeouts.MarketData.SymbolTimeout)" -ForegroundColor Cyan
Write-Host "  Trading SignalGeneration: $($timeoutConfig.Timeouts.Trading.SignalGeneration)" -ForegroundColor Cyan

# Verify the timeout values are reasonable (under 1 minute for individual operations)
$httpTimeout = [TimeSpan]::Parse($timeoutConfig.Timeouts.Http.StandardRequest)
$symbolTimeout = [TimeSpan]::Parse($timeoutConfig.Timeouts.MarketData.SymbolTimeout)
$signalTimeout = [TimeSpan]::Parse($timeoutConfig.Timeouts.Trading.SignalGeneration)

Write-Host "`nTimeout validation:" -ForegroundColor Yellow
if ($httpTimeout.TotalSeconds -le 60) {
    Write-Host "  ✅ HTTP timeout is reasonable: $($httpTimeout.TotalSeconds) seconds" -ForegroundColor Green
} else {
    Write-Host "  ❌ HTTP timeout is too long: $($httpTimeout.TotalSeconds) seconds" -ForegroundColor Red
}

if ($symbolTimeout.TotalSeconds -le 60) {
    Write-Host "  ✅ Symbol timeout is reasonable: $($symbolTimeout.TotalSeconds) seconds" -ForegroundColor Green
} else {
    Write-Host "  ❌ Symbol timeout is too long: $($symbolTimeout.TotalSeconds) seconds" -ForegroundColor Red
}

if ($signalTimeout.TotalSeconds -le 120) {
    Write-Host "  ✅ Signal generation timeout is reasonable: $($signalTimeout.TotalSeconds) seconds" -ForegroundColor Green
} else {
    Write-Host "  ❌ Signal generation timeout is too long: $($signalTimeout.TotalSeconds) seconds" -ForegroundColor Red
}

# Check if intelligent circuit breaker service exists
if (Test-Path "SmaTrendFollower.Console/Services/SymbolCircuitBreakerService.cs") {
    Write-Host "  ✅ Intelligent circuit breaker service created" -ForegroundColor Green
} else {
    Write-Host "  ❌ Intelligent circuit breaker service missing" -ForegroundColor Red
}

# Check if robust retry service exists
if (Test-Path "SmaTrendFollower.Console/Services/RobustRetryService.cs") {
    Write-Host "  ✅ Robust retry service created" -ForegroundColor Green
} else {
    Write-Host "  ❌ Robust retry service missing" -ForegroundColor Red
}

# Check if EnhancedSignalGenerator has been updated
$signalGeneratorContent = Get-Content "SmaTrendFollower.Console/Services/EnhancedSignalGenerator.cs" -Raw
if ($signalGeneratorContent -match "IntelligentSymbolCircuitBreakerService") {
    Write-Host "  ✅ EnhancedSignalGenerator updated with intelligent circuit breaker" -ForegroundColor Green
} else {
    Write-Host "  ❌ EnhancedSignalGenerator not updated with intelligent circuit breaker" -ForegroundColor Red
}

if ($signalGeneratorContent -match "RobustRetryService") {
    Write-Host "  ✅ EnhancedSignalGenerator updated with robust retry service" -ForegroundColor Green
} else {
    Write-Host "  ❌ EnhancedSignalGenerator not updated with robust retry service" -ForegroundColor Red
}

if ($signalGeneratorContent -match "_retryService\.ExecuteWithRetryAsync") {
    Write-Host "  ✅ EnhancedSignalGenerator using robust retry for data fetching" -ForegroundColor Green
} else {
    Write-Host "  ❌ EnhancedSignalGenerator not using robust retry for data fetching" -ForegroundColor Red
}

Write-Host "`nTimeout fix verification complete!" -ForegroundColor Green
Write-Host "The changes should prevent symbols like 'C' from taking 208+ seconds to process." -ForegroundColor Yellow
Write-Host "Expected behavior:" -ForegroundColor Yellow
Write-Host "  - Individual symbol attempts: 30 seconds max per attempt" -ForegroundColor Cyan
Write-Host "  - Robust retry: 3 attempts with exponential backoff" -ForegroundColor Cyan
Write-Host "  - Intelligent circuit breaker: Only for data issues, not infrastructure" -ForegroundColor Cyan
Write-Host "  - Fresh start each trading day: No missed opportunities" -ForegroundColor Cyan
Write-Host "  - Fallback support: Try multiple data sources if needed" -ForegroundColor Cyan
Write-Host "  - HTTP client timeout matches configuration (45 seconds)" -ForegroundColor Cyan
