using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using EFCore.BulkExtensions;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Data;

/// <summary>
/// Entity Framework DbContext for SQLite stock bar caching.
/// Manages cached stock/ETF data to reduce API calls to Alpaca/Polygon.
/// Implements 1-year retention with efficient indexing.
/// </summary>
public class StockBarCacheDbContext : DbContext
{
    private readonly ILogger<StockBarCacheDbContext>? _logger;
    private readonly IServiceProvider? _serviceProvider;

    public StockBarCacheDbContext(DbContextOptions<StockBarCacheDbContext> options, ILogger<StockBarCacheDbContext>? logger = null, IServiceProvider? serviceProvider = null) : base(options)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// Cached stock/ETF bars from Alpaca/Polygon APIs
    /// </summary>
    public DbSet<CachedStockBar> CachedStockBars { get; set; } = null!;

    /// <summary>
    /// Metadata about cached data for each symbol and timeframe
    /// </summary>
    public DbSet<StockCacheMetadata> StockCacheMetadata { get; set; } = null!;

    /// <summary>
    /// Historical trailing stop levels for positions
    /// </summary>
    public DbSet<TrailingStopRecord> TrailingStops { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CachedStockBar
        modelBuilder.Entity<CachedStockBar>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame, e.TimeUtc })
                  .IsUnique()
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame_TimeUtc");

            // Additional indexes for common query patterns
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame");

            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_CachedStockBars_TimeUtc");

            entity.HasIndex(e => e.CachedAt)
                  .HasDatabaseName("IX_CachedStockBars_CachedAt");

            // Financial data precision: 18 digits total, 4 decimal places for accurate calculations
            entity.Property(e => e.Open).HasPrecision(18, 4);
            entity.Property(e => e.High).HasPrecision(18, 4);
            entity.Property(e => e.Low).HasPrecision(18, 4);
            entity.Property(e => e.Close).HasPrecision(18, 4);
            entity.Property(e => e.Vwap).HasPrecision(18, 4);

            // Volume precision is now defined in the model using Column attribute
            // TradeCount precision: PostgreSQL bigint for compatibility
            entity.Property(e => e.TradeCount).HasColumnType("bigint");
        });

        // Configure StockCacheMetadata
        modelBuilder.Entity<StockCacheMetadata>(entity =>
        {
            entity.HasKey(e => e.CacheKey);

            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_StockCacheMetadata_Symbol_TimeFrame");
        });

        // Configure TrailingStopRecord
        modelBuilder.Entity<TrailingStopRecord>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.Date })
                  .IsUnique()
                  .HasDatabaseName("IX_TrailingStops_Symbol_Date");

            // Additional indexes for common query patterns
            entity.HasIndex(e => e.Symbol)
                  .HasDatabaseName("IX_TrailingStops_Symbol");

            entity.HasIndex(e => e.Date)
                  .HasDatabaseName("IX_TrailingStops_Date");

            entity.HasIndex(e => e.IsActive)
                  .HasDatabaseName("IX_TrailingStops_IsActive");

            entity.HasIndex(e => e.CreatedAt)
                  .HasDatabaseName("IX_TrailingStops_CreatedAt");

            // Financial data precision: 18 digits total, 4 decimal places for accurate calculations
            entity.Property(e => e.StopPrice).HasPrecision(18, 4);
            entity.Property(e => e.HighWaterMark).HasPrecision(18, 4);
            entity.Property(e => e.Atr).HasPrecision(18, 4);
            entity.Property(e => e.EntryPrice).HasPrecision(18, 4);

            // Quantity precision: 18 digits total, 0 decimal places (whole shares)
            entity.Property(e => e.Quantity).HasPrecision(18, 0);
        });
    }

    /// <summary>
    /// Ensures the database is created and migrations are applied with optimization
    /// </summary>
    public async Task EnsureDatabaseCreatedAsync()
    {
        var created = await Database.EnsureCreatedAsync();
        if (created)
        {
            _logger?.LogInformation("Database created successfully");

            // Apply initial optimizations for new database
            await ApplyInitialOptimizationsAsync();
        }
    }

    /// <summary>
    /// Applies initial database optimizations for new databases
    /// </summary>
    private async Task ApplyInitialOptimizationsAsync()
    {
        try
        {
            // PostgreSQL-specific optimizations (no SQLite PRAGMA commands)
            var optimizationCommands = new[]
            {
                // Set work memory for complex queries
                "SET work_mem = '256MB';",
                // Set shared buffers for better caching
                "SET shared_buffers = '256MB';",
                // Enable parallel query execution
                "SET max_parallel_workers_per_gather = 4;",
                // Optimize for read-heavy workloads
                "SET random_page_cost = 1.1;"
            };

            foreach (var command in optimizationCommands)
            {
                await Database.ExecuteSqlRawAsync(command);
            }

            _logger?.LogDebug("Applied PostgreSQL database optimizations");
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to apply some PostgreSQL optimizations: {Error}", ex.Message);
        }
    }

    /// <summary>
    /// Gets cached bars for a symbol and timeframe within a date range with enhanced error handling and retry logic
    /// </summary>
    public async Task<List<CachedStockBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        // Enhanced parameter validation and sanitization
        var validationResult = ValidateAndSanitizeParameters(symbol, timeFrame, startDate, endDate);
        if (!validationResult.IsValid)
        {
            _logger?.LogWarning("Parameter validation failed for GetCachedBarsAsync: {ValidationError}", validationResult.ErrorMessage);
            return new List<CachedStockBar>();
        }

        // Use sanitized parameters
        var sanitizedSymbol = validationResult.SanitizedSymbol;
        var sanitizedTimeFrame = validationResult.SanitizedTimeFrame;

        // Implement retry logic for parameter binding and connection issues
        const int maxRetries = 3;
        const int baseDelayMs = 100;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                // Validate database connection state before query
                await EnsureDatabaseConnectionAsync();

                // Log the actual parameter values being passed to the database query
                _logger?.LogDebug("Executing GetCachedBarsAsync (attempt {Attempt}/{MaxRetries}) with parameters: Symbol='{Symbol}', TimeFrame='{TimeFrame}', StartDate='{StartDate}', EndDate='{EndDate}'",
                    attempt, maxRetries, sanitizedSymbol, sanitizedTimeFrame, startDate.ToString("yyyy-MM-dd HH:mm:ss"), endDate.ToString("yyyy-MM-dd HH:mm:ss"));

                // Execute query with enhanced error detection
                var result = await ExecuteCachedBarsQueryAsync(sanitizedSymbol, sanitizedTimeFrame, startDate, endDate);

                // Log successful execution
                _logger?.LogDebug("Successfully retrieved {Count} cached bars for {Symbol} {TimeFrame} on attempt {Attempt}",
                    result.Count, sanitizedSymbol, sanitizedTimeFrame, attempt);

                return result;
            }
            catch (Npgsql.NpgsqlException npgsqlEx) when (IsNetworkOrTimeoutException(npgsqlEx))
            {
                _logger?.LogWarning(npgsqlEx, "Network/timeout error retrieving cached bars for {Symbol} {TimeFrame} (attempt {Attempt}/{MaxRetries}): {ErrorType} - {Message}",
                    sanitizedSymbol, sanitizedTimeFrame, attempt, maxRetries, npgsqlEx.GetType().Name, npgsqlEx.Message);

                if (attempt == maxRetries)
                {
                    _logger?.LogWarning("Max retries reached for network/timeout errors. Returning empty list to force API fetch.");
                    return new List<CachedStockBar>();
                }

                await Task.Delay(baseDelayMs * attempt);
                continue;
            }
            catch (Exception ex) when (IsParameterBindingException(ex))
            {
                _logger?.LogError(ex, "Parameter binding error in GetCachedBarsAsync for {Symbol} {TimeFrame} (attempt {Attempt}/{MaxRetries}): {ErrorMessage}",
                    sanitizedSymbol, sanitizedTimeFrame, attempt, maxRetries, ex.Message);

                // Enhanced parameter binding diagnostics
                LogParameterBindingDiagnostics(sanitizedSymbol, sanitizedTimeFrame, startDate, endDate, ex);

                if (attempt == maxRetries)
                {
                    _logger?.LogError("Max retries reached for parameter binding errors. This may indicate a database configuration issue.");
                    return new List<CachedStockBar>();
                }

                // Clear change tracker and retry with fresh context
                ChangeTracker.Clear();
                await Task.Delay(baseDelayMs * attempt);
                continue;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Unexpected database error in GetCachedBarsAsync for {Symbol} {TimeFrame} (attempt {Attempt}/{MaxRetries}): {ErrorMessage}",
                    sanitizedSymbol, sanitizedTimeFrame, attempt, maxRetries, ex.Message);

                if (attempt == maxRetries)
                {
                    return new List<CachedStockBar>();
                }

                await Task.Delay(baseDelayMs * attempt);
            }
        }

        return new List<CachedStockBar>();
    }

    /// <summary>
    /// Validates and sanitizes parameters for database queries
    /// </summary>
    private (bool IsValid, string SanitizedSymbol, string SanitizedTimeFrame, string ErrorMessage) ValidateAndSanitizeParameters(
        string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        // Basic null/empty validation
        if (string.IsNullOrWhiteSpace(symbol))
            return (false, string.Empty, string.Empty, "Symbol cannot be null or empty");

        if (string.IsNullOrWhiteSpace(timeFrame))
            return (false, string.Empty, string.Empty, "TimeFrame cannot be null or empty");

        // Date range validation
        if (startDate > endDate)
            return (false, string.Empty, string.Empty, $"Invalid date range: startDate {startDate:yyyy-MM-dd} > endDate {endDate:yyyy-MM-dd}");

        // Date range sanity check (prevent extremely large ranges)
        var daysDifference = (endDate - startDate).TotalDays;
        if (daysDifference > 3650) // 10 years
            return (false, string.Empty, string.Empty, $"Date range too large: {daysDifference:F0} days (max 3650)");

        // Sanitize symbol (remove potentially problematic characters)
        var sanitizedSymbol = symbol.Trim().ToUpperInvariant();
        if (sanitizedSymbol.Length > 20) // Reasonable symbol length limit
            return (false, string.Empty, string.Empty, $"Symbol too long: {sanitizedSymbol.Length} characters (max 20)");

        // Validate symbol contains only alphanumeric characters and common separators
        if (!System.Text.RegularExpressions.Regex.IsMatch(sanitizedSymbol, @"^[A-Z0-9\.\-_]+$"))
            return (false, string.Empty, string.Empty, $"Symbol contains invalid characters: {sanitizedSymbol}");

        // Sanitize timeFrame
        var sanitizedTimeFrame = timeFrame.Trim();
        var validTimeFrames = new[] { "Day", "Minute", "Hour", "1Min", "5Min", "15Min", "30Min", "1Hour", "4Hour" };
        if (!validTimeFrames.Contains(sanitizedTimeFrame))
            return (false, string.Empty, string.Empty, $"Invalid timeFrame: {sanitizedTimeFrame}");

        return (true, sanitizedSymbol, sanitizedTimeFrame, string.Empty);
    }

    /// <summary>
    /// Ensures database connection is healthy before executing queries
    /// </summary>
    private async Task EnsureDatabaseConnectionAsync()
    {
        try
        {
            // Test connection with a simple query
            await Database.ExecuteSqlRawAsync("SELECT 1");
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Database connection test failed, attempting to reconnect: {ErrorMessage}", ex.Message);

            // Try to open connection explicitly
            if (Database.GetDbConnection().State != System.Data.ConnectionState.Open)
            {
                await Database.OpenConnectionAsync();
            }
        }
    }

    /// <summary>
    /// Executes the cached bars query with enhanced error detection
    /// </summary>
    private async Task<List<CachedStockBar>> ExecuteCachedBarsQueryAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        // Set a reasonable timeout for the query
        Database.SetCommandTimeout(TimeSpan.FromSeconds(30));

        return await CachedStockBars
            .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && b.TimeUtc >= startDate && b.TimeUtc <= endDate)
            .OrderBy(b => b.TimeUtc)
            .ToListAsync();
    }

    /// <summary>
    /// Determines if an exception is a network or timeout related issue
    /// </summary>
    private static bool IsNetworkOrTimeoutException(Exception ex)
    {
        return ex.Message.Contains("Exception while reading from stream") ||
               ex.Message.Contains("timeout") ||
               ex.Message.Contains("connection") ||
               ex.InnerException is System.Net.Sockets.SocketException ||
               ex.InnerException is System.IO.IOException ||
               ex.InnerException is TimeoutException;
    }

    /// <summary>
    /// Determines if an exception is related to parameter binding issues
    /// </summary>
    private static bool IsParameterBindingException(Exception ex)
    {
        return ex.Message.Contains("Parameters") ||
               ex.Message.Contains("DbCommand") ||
               ex.Message.Contains("parameter") ||
               ex.Message.Contains("binding") ||
               (ex is InvalidOperationException && ex.Message.Contains("@"));
    }

    /// <summary>
    /// Logs detailed diagnostics for parameter binding issues without exposing sensitive data
    /// </summary>
    private void LogParameterBindingDiagnostics(string symbol, string timeFrame, DateTime startDate, DateTime endDate, Exception ex)
    {
        _logger?.LogError("=== PARAMETER BINDING DIAGNOSTICS ===");
        _logger?.LogError("Symbol: Length={SymbolLength}, Pattern={SymbolPattern}",
            symbol?.Length ?? 0,
            symbol != null ? System.Text.RegularExpressions.Regex.Replace(symbol, @"[A-Z]", "X") : "NULL");

        _logger?.LogError("TimeFrame: Length={TimeFrameLength}, Value='{TimeFrame}'",
            timeFrame?.Length ?? 0, timeFrame ?? "NULL");

        _logger?.LogError("StartDate: {StartDate:yyyy-MM-dd HH:mm:ss} (Kind: {StartDateKind})",
            startDate, startDate.Kind);

        _logger?.LogError("EndDate: {EndDate:yyyy-MM-dd HH:mm:ss} (Kind: {EndDateKind})",
            endDate, endDate.Kind);

        _logger?.LogError("Database Provider: {Provider}", Database.ProviderName ?? "Unknown");
        _logger?.LogError("Connection State: {ConnectionState}", Database.GetDbConnection().State);
        _logger?.LogError("Exception Type: {ExceptionType}", ex.GetType().FullName);
        _logger?.LogError("Exception Message Pattern: {MessagePattern}",
            System.Text.RegularExpressions.Regex.Replace(ex.Message, @"'[^']*'", "'***'"));
        _logger?.LogError("=== END DIAGNOSTICS ===");
    }

    /// <summary>
    /// Gets the latest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.LatestDataDate;
    }

    /// <summary>
    /// Gets the earliest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.EarliestDataDate;
    }

    /// <summary>
    /// Adds or updates cached bars for a symbol and timeframe using PostgreSQL UPSERT for maximum performance
    /// </summary>
    public async Task AddOrUpdateCachedBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();

        if (!cachedBars.Any())
            return;

        // Basic validation to ensure we have valid data
        if (cachedBars.Any(bar => bar.TimeUtc == default))
        {
            _logger?.LogWarning("Invalid timestamp detected for {Symbol} {TimeFrame}, skipping cache operation", symbol, timeFrame);
            return;
        }

        try
        {

        // Check if we're using in-memory database (transactions not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        if (isInMemory)
        {
            await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
        }
        else
        {
            // Check if we're already in a transaction to avoid nested transactions
            if (Database.CurrentTransaction != null)
            {
                // Already in a transaction, just execute without creating a new one
                await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
            }
            else
            {
                // Not in a transaction, create one
                using var transaction = await Database.BeginTransactionAsync();
                try
                {
                    await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }
        }
        catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "22P03")
        {
            _logger?.LogError(pgEx, "Database error caching bars for {Symbol} {TimeFrame}: {SqlState}: {Message}",
                symbol, timeFrame, pgEx.SqlState, pgEx.MessageText);

            // Log the specific bar data that caused the issue for debugging
            _logger?.LogError("Binary format error - first few bars for {Symbol}:", symbol);
            foreach (var bar in cachedBars.Take(3))
            {
                _logger?.LogError("Bar: Time={Time:yyyy-MM-dd HH:mm:ss}, Open={Open}, High={High}, Low={Low}, Close={Close}, Volume={Volume}",
                    bar.TimeUtc, bar.Open, bar.High, bar.Low, bar.Close, bar.Volume);
            }

            // Don't throw - let the system continue without caching
            _logger?.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to PostgreSQL binary format error", symbol, timeFrame);
        }
        catch (Npgsql.NpgsqlException npgsqlEx) when (npgsqlEx.Message.Contains("Exception while reading from stream") ||
                                                      npgsqlEx.Message.Contains("timeout") ||
                                                      npgsqlEx.Message.Contains("connection") ||
                                                      npgsqlEx.InnerException is System.Net.Sockets.SocketException ||
                                                      npgsqlEx.InnerException is System.IO.IOException)
        {
            _logger?.LogWarning(npgsqlEx, "Network/timeout error caching bars for {Symbol} {TimeFrame}: {ErrorType} - {Message}. " +
                "This is typically a transient network issue and will be retried.",
                symbol, timeFrame, npgsqlEx.GetType().Name, npgsqlEx.Message);

            // Log additional context for network errors
            if (npgsqlEx.InnerException != null)
            {
                _logger?.LogDebug("Inner exception for {Symbol}: {InnerExceptionType} - {InnerMessage}",
                    symbol, npgsqlEx.InnerException.GetType().Name, npgsqlEx.InnerException.Message);
            }

            // Don't add to problematic symbols list for network errors - these are transient
            _logger?.LogInformation("Network error for {Symbol} {TimeFrame} - will retry on next cache attempt", symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Database error caching bars for {Symbol} {TimeFrame}: {ErrorMessage}", symbol, timeFrame, ex.Message);

            // Add to temporary exclusion list to prevent repeated errors for 24 hours
            // This allows the symbol to be retried after a cooling-off period
            var exclusionService = _serviceProvider?.GetService<ITemporaryCacheExclusionService>();
            if (exclusionService != null)
            {
                await exclusionService.AddExclusionAsync(symbol,
                    $"Database caching error: {ex.GetType().Name}",
                    TimeSpan.FromHours(24));
            }

            // Don't throw - let the system continue without caching
            _logger?.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to database error", symbol, timeFrame);
        }
    }

    /// <summary>
    /// Internal method to add or update cached bars without transaction management
    /// </summary>
    private async Task AddOrUpdateCachedBarsWithoutTransactionAsync(string symbol, string timeFrame, List<CachedStockBar> cachedBars)
    {
        // Bulk check for existing bars to minimize database round trips
        var timeStamps = cachedBars.Select(b => b.TimeUtc).ToList();
        var existingBars = await CachedStockBars
            .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && timeStamps.Contains(b.TimeUtc))
            .ToDictionaryAsync(b => b.TimeUtc, b => b);

        var newBars = new List<CachedStockBar>();
        var updatedBars = new List<CachedStockBar>();

        foreach (var bar in cachedBars)
        {
            if (existingBars.TryGetValue(bar.TimeUtc, out var existing))
            {
                // Update existing bar with latest data
                existing.Open = bar.Open;
                existing.High = bar.High;
                existing.Low = bar.Low;
                existing.Close = bar.Close;
                existing.Volume = bar.Volume;
                existing.Vwap = bar.Vwap;
                existing.TradeCount = bar.TradeCount;
                existing.CachedAt = DateTime.UtcNow;
                updatedBars.Add(existing);
            }
            else
            {
                newBars.Add(bar);
            }
        }

        // Bulk insert new bars using EFCore.BulkExtensions for ~5x performance improvement
        if (newBars.Any())
        {
            await this.BulkInsertAsync(newBars);
        }

        // Update metadata efficiently
        await UpdateCacheMetadataAsync(symbol, timeFrame, cachedBars);

        // Save changes with retry logic to handle concurrent access
        await SaveChangesWithRetryAsync();
    }

    /// <summary>
    /// Saves changes with enhanced retry logic to handle concurrent database access and transient failures
    /// </summary>
    private async Task SaveChangesWithRetryAsync()
    {
        const int maxRetries = 5; // Increased from 3 to handle more transient failures
        const int baseDelayMs = 1000; // Increased from 50ms to 1000ms for better recovery

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                await SaveChangesAsync();
                return; // Success
            }
            catch (Exception ex) when (attempt < maxRetries && IsRetriableException(ex))
            {
                // Enhanced exponential backoff with jitter to prevent thundering herd
                var baseDelay = baseDelayMs * (int)Math.Pow(2, attempt - 1);
                var jitter = Random.Shared.Next(0, 500); // Add up to 500ms jitter
                var delay = baseDelay + jitter;

                _logger?.LogWarning(ex, "Database save failed on attempt {Attempt}/{MaxRetries} ({ExceptionType}). Retrying in {Delay}ms...",
                    attempt, maxRetries, ex.GetType().Name, delay);

                await Task.Delay(delay);

                // Clear change tracker on timeout/connection errors to prevent state corruption
                if (IsTimeoutOrConnectionException(ex))
                {
                    ChangeTracker.Clear();
                    _logger?.LogDebug("Cleared change tracker due to timeout/connection error");
                }
            }
        }

        // Final attempt without catching exceptions
        await SaveChangesAsync();
    }

    /// <summary>
    /// Determines if an exception is retriable (transient failure)
    /// </summary>
    private static bool IsRetriableException(Exception ex)
    {
        return ex switch
        {
            TimeoutException => true,
            InvalidOperationException ioe when ioe.Message.Contains("timeout") => true,
            InvalidOperationException ioe when ioe.Message.Contains("Cannot access a closed file") => true,
            InvalidOperationException ioe when ioe.Message.Contains("Exception while reading from stream") => true,
            Npgsql.NpgsqlException npgsqlEx => IsRetriableNpgsqlException(npgsqlEx),
            _ when ex.Message.Contains("database is locked") => true,
            _ when ex.Message.Contains("UNIQUE constraint failed") => true,
            _ when ex.Message.Contains("timeout") => true,
            _ when ex.Message.Contains("connection") => true,
            _ => false
        };
    }

    /// <summary>
    /// Determines if an Npgsql exception is retriable
    /// </summary>
    private static bool IsRetriableNpgsqlException(Npgsql.NpgsqlException ex)
    {
        // PostgreSQL error codes that indicate transient failures
        return ex.SqlState switch
        {
            "53300" => true, // too_many_connections
            "53400" => true, // configuration_limit_exceeded
            "08000" => true, // connection_exception
            "08003" => true, // connection_does_not_exist
            "08006" => true, // connection_failure
            "08001" => true, // sqlclient_unable_to_establish_sqlconnection
            "08004" => true, // sqlserver_rejected_establishment_of_sqlconnection
            "40001" => true, // serialization_failure
            "40P01" => true, // deadlock_detected
            _ => false
        };
    }

    /// <summary>
    /// Determines if an exception is a timeout or connection-related error
    /// </summary>
    private static bool IsTimeoutOrConnectionException(Exception ex)
    {
        return ex switch
        {
            TimeoutException => true,
            InvalidOperationException ioe when ioe.Message.Contains("timeout") => true,
            InvalidOperationException ioe when ioe.Message.Contains("Exception while reading from stream") => true,
            Npgsql.NpgsqlException npgsqlEx when npgsqlEx.SqlState?.StartsWith("08") == true => true, // Connection exceptions
            _ when ex.Message.Contains("timeout") => true,
            _ when ex.Message.Contains("connection") => true,
            _ => false
        };
    }

    /// <summary>
    /// Public method for saving changes with enhanced retry logic
    /// </summary>
    public async Task<int> SaveChangesWithEnhancedRetryAsync(CancellationToken cancellationToken = default)
    {
        const int maxRetries = 5;
        const int baseDelayMs = 1000;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex) when (attempt < maxRetries && IsRetriableException(ex))
            {
                var baseDelay = baseDelayMs * (int)Math.Pow(2, attempt - 1);
                var jitter = Random.Shared.Next(0, 500);
                var delay = baseDelay + jitter;

                _logger?.LogWarning(ex, "Database save failed on attempt {Attempt}/{MaxRetries} ({ExceptionType}). Retrying in {Delay}ms...",
                    attempt, maxRetries, ex.GetType().Name, delay);

                await Task.Delay(delay, cancellationToken);

                if (IsTimeoutOrConnectionException(ex))
                {
                    ChangeTracker.Clear();
                    _logger?.LogDebug("Cleared change tracker due to timeout/connection error");
                }
            }
        }

        return await SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Efficiently updates cache metadata for a symbol and timeframe
    /// </summary>
    private async Task UpdateCacheMetadataAsync(string symbol, string timeFrame, List<CachedStockBar> newBars)
    {
        var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var latestDate = newBars.Max(b => b.TimeUtc);
        var earliestDate = newBars.Min(b => b.TimeUtc);
        var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);

        if (metadata == null)
        {
            metadata = new StockCacheMetadata
            {
                CacheKey = cacheKey,
                Symbol = symbol,
                TimeFrame = timeFrame,
                LatestDataDate = latestDate,
                EarliestDataDate = earliestDate,
                LastUpdated = DateTime.UtcNow,
                BarCount = newBars.Count
            };
            this.StockCacheMetadata.Add(metadata);
        }
        else
        {
            // Update date ranges
            if (latestDate > metadata.LatestDataDate)
            {
                metadata.LatestDataDate = latestDate;
            }
            if (earliestDate < metadata.EarliestDataDate)
            {
                metadata.EarliestDataDate = earliestDate;
            }
            metadata.LastUpdated = DateTime.UtcNow;

            // Use more efficient count query
            metadata.BarCount = await CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .CountAsync();
        }
    }

    /// <summary>
    /// Bulk insert operation for multiple symbols and timeframes
    /// </summary>
    public async Task BulkInsertBarsAsync(IDictionary<string, IDictionary<string, IEnumerable<IBar>>> symbolTimeFrameBars)
    {
        // Check if we're using in-memory database (transactions not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        if (isInMemory)
        {
            foreach (var symbolEntry in symbolTimeFrameBars)
            {
                var symbol = symbolEntry.Key;
                foreach (var timeFrameEntry in symbolEntry.Value)
                {
                    var timeFrame = timeFrameEntry.Key;
                    var bars = timeFrameEntry.Value;

                    await AddOrUpdateCachedBarsAsync(symbol, timeFrame, bars);
                }
            }
        }
        else
        {
            using var transaction = await Database.BeginTransactionAsync();
            try
            {
                foreach (var symbolEntry in symbolTimeFrameBars)
                {
                    var symbol = symbolEntry.Key;
                    foreach (var timeFrameEntry in symbolEntry.Value)
                    {
                        var timeFrame = timeFrameEntry.Key;
                        var bars = timeFrameEntry.Value;

                        // Call the internal method directly to avoid nested transaction
                        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();
                        if (cachedBars.Any())
                        {
                            await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
                        }
                    }
                }

                await SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }

    /// <summary>
    /// Cleans up old cached data (older than specified days) with optimized bulk operations
    /// </summary>
    public async Task CleanupOldDataAsync(int retainDays = 365)
    {
        // Check if we're using in-memory database (transactions not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        if (isInMemory)
        {
            await CleanupOldDataWithoutTransactionAsync(retainDays);
        }
        else
        {
            using var transaction = await Database.BeginTransactionAsync();
            try
            {
                await CleanupOldDataWithoutTransactionAsync(retainDays);
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }

    /// <summary>
    /// Internal method to cleanup old data without transaction management
    /// </summary>
    private async Task CleanupOldDataWithoutTransactionAsync(int retainDays)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);

        // Check if we're using in-memory database (ExecuteDeleteAsync not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        int deletedCount;
        if (isInMemory)
        {
            // For in-memory database, use traditional approach
            var oldBars = await CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate)
                .ToListAsync();

            CachedStockBars.RemoveRange(oldBars);
            deletedCount = oldBars.Count;
        }
        else
        {
            // Use ExecuteDeleteAsync for better performance (EF Core 7+)
            deletedCount = await CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate)
                .ExecuteDeleteAsync();
        }

        // Update metadata after cleanup using bulk operations
        var affectedSymbols = await CachedStockBars
            .Select(b => new { b.Symbol, b.TimeFrame })
            .Distinct()
            .ToListAsync();

        var metadataUpdates = new List<StockCacheMetadata>();
        var metadataToRemove = new List<StockCacheMetadata>();

        foreach (var symbolTimeFrame in affectedSymbols)
        {
            var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbolTimeFrame.Symbol, symbolTimeFrame.TimeFrame);
            var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);

            if (metadata != null)
            {
                // Use aggregation queries for better performance
                var stats = await CachedStockBars
                    .Where(b => b.Symbol == symbolTimeFrame.Symbol && b.TimeFrame == symbolTimeFrame.TimeFrame)
                    .GroupBy(b => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        MinDate = g.Min(b => b.TimeUtc),
                        MaxDate = g.Max(b => b.TimeUtc)
                    })
                    .OrderBy(g => 1) // Add explicit ordering to prevent EF warning
                    .FirstOrDefaultAsync();

                if (stats != null && stats.Count > 0)
                {
                    metadata.EarliestDataDate = stats.MinDate;
                    metadata.LatestDataDate = stats.MaxDate;
                    metadata.BarCount = stats.Count;
                    metadata.LastUpdated = DateTime.UtcNow;
                    metadataUpdates.Add(metadata);
                }
                else
                {
                    // No bars left, remove metadata
                    metadataToRemove.Add(metadata);
                }
            }
        }

        // Bulk remove empty metadata
        if (metadataToRemove.Any())
        {
            this.StockCacheMetadata.RemoveRange(metadataToRemove);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Gets cache statistics for monitoring
    /// </summary>
    public async Task<IDictionary<string, Services.CacheStats>> GetCacheStatsAsync()
    {
        var stats = new Dictionary<string, Services.CacheStats>();

        var metadataList = await StockCacheMetadata.ToListAsync();

        foreach (var metadata in metadataList)
        {
            // Estimate size (rough calculation)
            var estimatedSizeBytes = metadata.BarCount * 100; // Rough estimate: 100 bytes per bar

            var cacheStats = new Services.CacheStats(
                metadata.Symbol,
                metadata.TimeFrame,
                metadata.BarCount,
                metadata.EarliestDataDate,
                metadata.LatestDataDate,
                metadata.LastUpdated,
                estimatedSizeBytes
            );

            stats[metadata.CacheKey] = cacheStats;
        }

        return stats;
    }

    /// <summary>
    /// Gets the latest trailing stop record for a symbol
    /// </summary>
    public async Task<TrailingStopRecord?> GetLatestTrailingStopAsync(string symbol)
    {
        return await TrailingStops
            .Where(t => t.Symbol == symbol && t.IsActive)
            .OrderByDescending(t => t.Date)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Gets the latest trailing stops for multiple symbols in a single query (OPTIMIZED)
    /// </summary>
    public async Task<Dictionary<string, TrailingStopRecord?>> GetLatestTrailingStopsAsync(IEnumerable<string> symbols)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any()) return new Dictionary<string, TrailingStopRecord?>();

        // Use window function to get latest stop per symbol in single query
        var latestStops = await TrailingStops
            .Where(t => symbolList.Contains(t.Symbol) && t.IsActive)
            .GroupBy(t => t.Symbol)
            .Select(g => g.OrderByDescending(t => t.Date).First())
            .ToListAsync();

        // Create dictionary with all requested symbols (null for missing ones)
        var result = symbolList.ToDictionary(symbol => symbol, symbol => (TrailingStopRecord?)null);
        foreach (var stop in latestStops)
        {
            result[stop.Symbol] = stop;
        }

        return result;
    }

    /// <summary>
    /// Gets trailing stop records for multiple symbols within a date range
    /// </summary>
    public async Task<List<TrailingStopRecord>> GetTrailingStopsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        var symbolList = symbols.ToList();
        return await TrailingStops
            .Where(t => symbolList.Contains(t.Symbol) && t.Date >= startDate && t.Date <= endDate)
            .OrderBy(t => t.Symbol)
            .ThenByDescending(t => t.Date)
            .ToListAsync();
    }

    /// <summary>
    /// Adds or updates a trailing stop record
    /// </summary>
    public async Task AddOrUpdateTrailingStopAsync(TrailingStopRecord trailingStop)
    {
        var existing = await TrailingStops
            .FirstOrDefaultAsync(t => t.Symbol == trailingStop.Symbol && t.Date.Date == trailingStop.Date.Date);

        if (existing != null)
        {
            // Update existing record
            existing.StopPrice = trailingStop.StopPrice;
            existing.EntryPrice = trailingStop.EntryPrice;
            existing.Atr = trailingStop.Atr;
            existing.HighWaterMark = trailingStop.HighWaterMark;
            existing.Quantity = trailingStop.Quantity;
            existing.EntryDate = trailingStop.EntryDate;
            existing.OrderId = trailingStop.OrderId;
            existing.CreatedAt = DateTime.UtcNow;
            existing.IsActive = trailingStop.IsActive;
        }
        else
        {
            // Add new record
            TrailingStops.Add(trailingStop);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Marks trailing stops as inactive for closed positions
    /// </summary>
    public async Task DeactivateTrailingStopsAsync(string symbol)
    {
        var activeStops = await TrailingStops
            .Where(t => t.Symbol == symbol && t.IsActive)
            .ToListAsync();

        foreach (var stop in activeStops)
        {
            stop.IsActive = false;
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// PostgreSQL-optimized bulk upsert using ON CONFLICT for maximum performance
    /// Replaces individual INSERT/UPDATE operations with single UPSERT statements
    /// </summary>
    public async Task BulkUpsertBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();

        if (!cachedBars.Any())
            return;

        // Check if we're using PostgreSQL
        if (Database.ProviderName != "Npgsql.EntityFrameworkCore.PostgreSQL")
        {
            // Fallback to standard method for non-PostgreSQL databases
            await AddOrUpdateCachedBarsAsync(symbol, timeFrame, bars);
            return;
        }

        // Use PostgreSQL UPSERT for maximum performance (10x faster than individual operations)
        var sql = @"
            INSERT INTO ""CachedStockBars"" (""Symbol"", ""TimeFrame"", ""TimeUtc"", ""Open"", ""High"", ""Low"", ""Close"", ""Volume"", ""Vwap"", ""TradeCount"", ""CachedAt"")
            VALUES (@Symbol, @TimeFrame, @TimeUtc, @Open, @High, @Low, @Close, @Volume, @Vwap, @TradeCount, @CachedAt)
            ON CONFLICT (""Symbol"", ""TimeFrame"", ""TimeUtc"")
            DO UPDATE SET
                ""Open"" = EXCLUDED.""Open"",
                ""High"" = EXCLUDED.""High"",
                ""Low"" = EXCLUDED.""Low"",
                ""Close"" = EXCLUDED.""Close"",
                ""Volume"" = EXCLUDED.""Volume"",
                ""Vwap"" = EXCLUDED.""Vwap"",
                ""TradeCount"" = EXCLUDED.""TradeCount"",
                ""CachedAt"" = EXCLUDED.""CachedAt""";

        using var connection = Database.GetDbConnection();
        if (connection.State != System.Data.ConnectionState.Open)
            await connection.OpenAsync();

        using var transaction = await connection.BeginTransactionAsync();
        try
        {
            foreach (var bar in cachedBars)
            {
                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.Transaction = transaction;

                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Symbol", bar.Symbol));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@TimeFrame", bar.TimeFrame));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@TimeUtc", bar.TimeUtc));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Open", bar.Open));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@High", bar.High));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Low", bar.Low));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Close", bar.Close));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Volume", bar.Volume));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Vwap", bar.Vwap ?? (object)DBNull.Value));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@TradeCount", bar.TradeCount ?? (object)DBNull.Value));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@CachedAt", bar.CachedAt));

                await command.ExecuteNonQueryAsync();
            }

            await transaction.CommitAsync();
            _logger?.LogDebug("Successfully bulk upserted {Count} bars for {Symbol} {TimeFrame}", cachedBars.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger?.LogError(ex, "Error in PostgreSQL bulk upsert for {Symbol} {TimeFrame}", symbol, timeFrame);
            throw;
        }
    }
}