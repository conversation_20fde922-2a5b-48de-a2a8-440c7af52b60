using Microsoft.Extensions.Logging;
using SmaTrendFollower.Console.Configuration;
using System.Net;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Robust retry service with intelligent backoff and fallback mechanisms.
/// Works with IntelligentSymbolCircuitBreakerService to provide comprehensive error handling.
/// </summary>
public class RobustRetryService
{
    private readonly ILogger<RobustRetryService> _logger;
    private readonly TimeoutConfiguration _timeouts;
    private readonly IntelligentSymbolCircuitBreakerService _circuitBreaker;

    public RobustRetryService(
        ILogger<RobustRetryService> logger,
        TimeoutConfiguration timeouts,
        IntelligentSymbolCircuitBreakerService circuitBreaker)
    {
        _logger = logger;
        _timeouts = timeouts;
        _circuitBreaker = circuitBreaker;
    }

    /// <summary>
    /// Execute an operation with robust retry logic and intelligent error handling
    /// </summary>
    public async Task<T> ExecuteWithRetryAsync<T>(
        string symbol,
        Func<CancellationToken, Task<T>> operation,
        CancellationToken cancellationToken = default,
        int maxAttempts = 3,
        TimeSpan? baseDelay = null)
    {
        // Check circuit breaker first
        if (_circuitBreaker.ShouldSkipSymbol(symbol))
        {
            throw new InvalidOperationException($"Symbol {symbol} is circuit broken");
        }

        var delay = baseDelay ?? TimeSpan.FromSeconds(2);
        var timeout = _timeouts.MarketData.SymbolTimeout;
        Exception? lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++)
        {
            try
            {
                _logger.LogDebug("Attempting operation for {Symbol} (attempt {Attempt}/{MaxAttempts})", 
                    symbol, attempt, maxAttempts);

                // Create timeout for this specific attempt
                using var timeoutCts = new CancellationTokenSource(timeout);
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken, timeoutCts.Token);

                var startTime = DateTime.UtcNow;
                var result = await operation(combinedCts.Token);
                var duration = DateTime.UtcNow - startTime;

                // Record success
                _circuitBreaker.RecordSuccess(symbol);
                
                _logger.LogDebug("Operation succeeded for {Symbol} in {Duration}ms (attempt {Attempt})", 
                    symbol, duration.TotalMilliseconds, attempt);

                return result;
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                // Overall cancellation - don't retry
                _logger.LogDebug("Operation cancelled for {Symbol} due to overall cancellation", symbol);
                throw;
            }
            catch (OperationCanceledException ex)
            {
                // Timeout - this is an infrastructure issue, retry aggressively
                lastException = ex;
                var duration = DateTime.UtcNow.Subtract(DateTime.UtcNow.AddMilliseconds(-timeout.TotalMilliseconds));
                
                _logger.LogWarning("Timeout for {Symbol} on attempt {Attempt}/{MaxAttempts} after {Timeout}s", 
                    symbol, attempt, maxAttempts, timeout.TotalSeconds);

                // Record as infrastructure failure (will not circuit break)
                _circuitBreaker.RecordFailure(symbol, duration, "Timeout", ex);

                if (attempt < maxAttempts)
                {
                    await DelayWithBackoff(attempt, delay, cancellationToken);
                }
            }
            catch (HttpRequestException ex) when (IsRetryableHttpError(ex))
            {
                // Retryable HTTP error - infrastructure issue
                lastException = ex;
                
                _logger.LogWarning("Retryable HTTP error for {Symbol} on attempt {Attempt}/{MaxAttempts}: {Error}", 
                    symbol, attempt, maxAttempts, ex.Message);

                // Record as infrastructure failure (will not circuit break)
                _circuitBreaker.RecordFailure(symbol, TimeSpan.Zero, ex.Message, ex);

                if (attempt < maxAttempts)
                {
                    await DelayWithBackoff(attempt, delay, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                // Other exceptions - let circuit breaker classify them
                lastException = ex;
                
                _logger.LogWarning(ex, "Error for {Symbol} on attempt {Attempt}/{MaxAttempts}: {Error}", 
                    symbol, attempt, maxAttempts, ex.Message);

                // Let circuit breaker decide if this should circuit break
                _circuitBreaker.RecordFailure(symbol, TimeSpan.Zero, ex.Message, ex);

                // Check if circuit breaker opened due to this error
                if (_circuitBreaker.ShouldSkipSymbol(symbol))
                {
                    _logger.LogWarning("Circuit breaker opened for {Symbol}, stopping retries", symbol);
                    throw;
                }

                if (attempt < maxAttempts)
                {
                    await DelayWithBackoff(attempt, delay, cancellationToken);
                }
            }
        }

        // All attempts failed
        _logger.LogError("All {MaxAttempts} attempts failed for {Symbol}. Last error: {Error}", 
            maxAttempts, symbol, lastException?.Message);
        
        throw lastException ?? new InvalidOperationException($"All retry attempts failed for {symbol}");
    }

    /// <summary>
    /// Execute with fallback to alternative data source
    /// </summary>
    public async Task<T> ExecuteWithFallbackAsync<T>(
        string symbol,
        Func<CancellationToken, Task<T>> primaryOperation,
        Func<CancellationToken, Task<T>> fallbackOperation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await ExecuteWithRetryAsync(symbol, primaryOperation, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Primary operation failed for {Symbol}, trying fallback: {Error}", 
                symbol, ex.Message);

            try
            {
                return await ExecuteWithRetryAsync(symbol, fallbackOperation, cancellationToken, maxAttempts: 2);
            }
            catch (Exception fallbackEx)
            {
                _logger.LogError("Both primary and fallback operations failed for {Symbol}. " +
                               "Primary: {PrimaryError}, Fallback: {FallbackError}", 
                    symbol, ex.Message, fallbackEx.Message);
                throw;
            }
        }
    }

    private async Task DelayWithBackoff(int attempt, TimeSpan baseDelay, CancellationToken cancellationToken)
    {
        // Exponential backoff with jitter
        var delay = TimeSpan.FromMilliseconds(
            baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1) + 
            Random.Shared.Next(0, 1000)); // Add jitter

        // Cap at 30 seconds
        if (delay > TimeSpan.FromSeconds(30))
            delay = TimeSpan.FromSeconds(30);

        _logger.LogDebug("Waiting {Delay}ms before retry attempt {NextAttempt}", 
            delay.TotalMilliseconds, attempt + 1);

        await Task.Delay(delay, cancellationToken);
    }

    private static bool IsRetryableHttpError(HttpRequestException ex)
    {
        var message = ex.Message.ToLowerInvariant();
        
        // Retryable HTTP errors (infrastructure issues)
        return message.Contains("timeout") ||
               message.Contains("connection") ||
               message.Contains("network") ||
               message.Contains("socket") ||
               message.Contains("dns") ||
               message.Contains("502") ||
               message.Contains("503") ||
               message.Contains("504") ||
               message.Contains("429"); // Rate limit
    }
}
