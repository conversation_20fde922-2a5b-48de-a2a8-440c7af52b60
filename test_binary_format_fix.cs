using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test to verify the PostgreSQL binary format error fix for CachedStockBars
/// </summary>
public class BinaryFormatFixTest
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 Testing PostgreSQL binary format fix for CachedStockBars...");

        var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                var configuration = context.Configuration;

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Add database context
                services.AddDbContextFactory<StockBarCacheDbContext>(options =>
                {
                    var connectionString = configuration.GetConnectionString("StockBarCache") ??
                        "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";
                    options.UseNpgsql(connectionString);
                    options.EnableSensitiveDataLogging(true);
                    options.EnableDetailedErrors(true);
                    // Suppress Entity Framework warnings about First/FirstOrDefault without OrderBy
                    options.ConfigureWarnings(warnings =>
                    {
                        warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
                    });
                });
            })
            .Build();

        using var scope = host.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<BinaryFormatFixTest>>();
        var contextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<StockBarCacheDbContext>>();

        try
        {
            await TestBinaryFormatFix(contextFactory, logger);
            Console.WriteLine("✅ All tests passed! Binary format error should be fixed.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed: {ex.Message}");
            logger.LogError(ex, "Test failed");
            Environment.Exit(1);
        }
    }

    private static async Task TestBinaryFormatFix(IDbContextFactory<StockBarCacheDbContext> contextFactory, ILogger logger)
    {
        using var context = await contextFactory.CreateDbContextAsync();

        // Test 1: Create test bar data that previously caused binary format errors
        logger.LogInformation("🔍 Test 1: Creating test bar data for AMBP symbol...");
        
        var testBars = new List<TestBar>
        {
            new TestBar
            {
                Symbol = "AMBP",
                TimeUtc = new DateTime(2024, 9, 27, 13, 30, 0, DateTimeKind.Utc),
                Open = 3.74m,
                High = 3.75m,
                Low = 3.74m,
                Close = 3.74m,
                Volume = 19581m,
                Vwap = 3.745m,
                TradeCount = 150
            },
            new TestBar
            {
                Symbol = "AMBP", 
                TimeUtc = new DateTime(2024, 9, 27, 13, 31, 0, DateTimeKind.Utc),
                Open = 3.75m,
                High = 3.76m,
                Low = 3.75m,
                Close = 3.75m,
                Volume = 2520m,
                Vwap = 3.753m,
                TradeCount = 25
            }
        };

        // Test 2: Try to cache the bars (this previously caused the binary format error)
        logger.LogInformation("🔍 Test 2: Attempting to cache bars (this previously failed)...");
        
        try
        {
            await context.AddOrUpdateCachedBarsAsync("AMBP", "Minute", testBars);
            logger.LogInformation("✅ Successfully cached bars without binary format error!");
        }
        catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "22P03")
        {
            logger.LogError("❌ Binary format error still occurs: {Message}", pgEx.MessageText);
            throw new Exception($"Binary format error not fixed: {pgEx.MessageText}");
        }

        // Test 3: Verify the data was stored correctly
        logger.LogInformation("🔍 Test 3: Verifying stored data integrity...");
        
        var cachedBars = await context.GetCachedBarsAsync("AMBP", "Minute", 
            new DateTime(2024, 9, 27, 13, 30, 0, DateTimeKind.Utc),
            new DateTime(2024, 9, 27, 13, 32, 0, DateTimeKind.Utc));

        if (cachedBars.Count != 2)
        {
            throw new Exception($"Expected 2 cached bars, but got {cachedBars.Count}");
        }

        var firstBar = cachedBars.First();
        if (firstBar.Volume != 19581m)
        {
            throw new Exception($"Volume mismatch: expected 19581, got {firstBar.Volume}");
        }

        if (firstBar.TradeCount != 150)
        {
            throw new Exception($"TradeCount mismatch: expected 150, got {firstBar.TradeCount}");
        }

        logger.LogInformation("✅ Data integrity verified - Volume and TradeCount stored correctly!");

        // Test 4: Test with null TradeCount (edge case)
        logger.LogInformation("🔍 Test 4: Testing null TradeCount handling...");
        
        var nullTradeCountBar = new TestBar
        {
            Symbol = "AMBP",
            TimeUtc = new DateTime(2024, 9, 27, 13, 32, 0, DateTimeKind.Utc),
            Open = 3.76m,
            High = 3.76m,
            Low = 3.74m,
            Close = 3.74m,
            Volume = 3518m,
            Vwap = 3.75m,
            TradeCount = 0 // This will be stored as null
        };

        await context.AddOrUpdateCachedBarsAsync("AMBP", "Minute", new[] { nullTradeCountBar });
        logger.LogInformation("✅ Null TradeCount handled correctly!");

        // Clean up test data
        logger.LogInformation("🧹 Cleaning up test data...");
        await context.Database.ExecuteSqlRawAsync("DELETE FROM \"CachedStockBars\" WHERE \"Symbol\" = 'AMBP'");
        await context.Database.ExecuteSqlRawAsync("DELETE FROM \"StockCacheMetadata\" WHERE \"Symbol\" = 'AMBP'");
    }
}

/// <summary>
/// Test implementation of IBar for testing
/// </summary>
public class TestBar : IBar
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public decimal Volume { get; set; }
    public decimal Vwap { get; set; }
    public ulong TradeCount { get; set; }
}
