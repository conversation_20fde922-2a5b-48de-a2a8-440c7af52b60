using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Console.Configuration;
using SmaTrendFollower.Console.Services;
using Alpaca.Markets;
using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced signal generator with momentum and volatility filtering.
/// Combines parallel processing with intelligent market condition filtering.
/// </summary>
public sealed class EnhancedSignalGenerator : ISignalGenerator, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILiveStateStore _liveStateStore;
    private readonly IMomentumFilter _momentumFilter;
    private readonly IVolatilityFilter _volatilityFilter;
    private readonly IPositionSizer _positionSizer;
    private readonly ILogger<EnhancedSignalGenerator> _logger;
    private readonly ParallelOptions _parallelOptions;
    private readonly SemaphoreSlim _rateGate = new(80); // OPTIMIZED: Increased to 80 concurrent calls for large symbol universes
    private readonly TimeoutConfiguration _timeouts;
    private readonly IntelligentSymbolCircuitBreakerService _circuitBreaker;
    private readonly RobustRetryService _retryService;

    public EnhancedSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILiveStateStore liveStateStore,
        IMomentumFilter momentumFilter,
        IVolatilityFilter volatilityFilter,
        IPositionSizer positionSizer,
        ILogger<EnhancedSignalGenerator> logger,
        IntelligentSymbolCircuitBreakerService circuitBreaker,
        RobustRetryService retryService,
        TimeoutConfiguration? timeouts = null)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _liveStateStore = liveStateStore;
        _momentumFilter = momentumFilter;
        _volatilityFilter = volatilityFilter;
        _positionSizer = positionSizer;
        _logger = logger;
        _circuitBreaker = circuitBreaker;
        _retryService = retryService;
        _timeouts = timeouts ?? new TimeoutConfiguration();

        _parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount,
            CancellationToken = CancellationToken.None
        };
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        var totalStopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting enhanced signal generation with momentum and volatility filtering");

            // Step 1: Check market conditions first
            var marketVolatility = await _volatilityFilter.GetMarketVolatilityAsync();
            if (!marketVolatility.IsEligible)
            {
                _logger.LogWarning("Market conditions unfavorable for trading: {Reason}", marketVolatility.Reason);
                return Enumerable.Empty<TradingSignal>();
            }

            _logger.LogInformation("Market conditions favorable: {Regime} volatility, VIX {VIX:F1}",
                marketVolatility.Regime, marketVolatility.VixLevel);

            // Step 2: Get universe and fetch data with error handling
            List<string> symbolList;
            try
            {
                var symbols = await _universeProvider.GetSymbolsAsync();
                symbolList = symbols.Distinct().ToList(); // Remove duplicates

                var originalCount = symbols.Count();
                if (originalCount != symbolList.Count)
                {
                    _logger.LogWarning("Removed {DuplicateCount} duplicate symbols from universe (original: {OriginalCount}, unique: {UniqueCount})",
                        originalCount - symbolList.Count, originalCount, symbolList.Count);
                }

                if (symbolList.Count == 0)
                {
                    _logger.LogWarning("No symbols available from universe provider");
                    return Enumerable.Empty<TradingSignal>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get symbol universe. Using fallback symbols.");
                symbolList = new List<string> { "SPY", "QQQ", "IWM" }; // Fallback to major ETFs
            }

            _logger.LogInformation("Screening {Count} symbols with enhanced filtering", symbolList.Count);

            // Log the symbols being processed
            _logger.LogInformation("Processing symbols: {Symbols}", string.Join(", ", symbolList));

            // Step 3: Fetch data with error handling
            ConcurrentDictionary<string, List<IBar>> symbolDataMap;
            try
            {
                symbolDataMap = await FetchDataInParallelAsync(symbolList, cancellationToken);
                _logger.LogInformation("Fetched data for {Count}/{Total} symbols", symbolDataMap.Count, symbolList.Count);

                if (symbolDataMap.Count == 0)
                {
                    _logger.LogError("No market data could be fetched for any symbols");
                    return Enumerable.Empty<TradingSignal>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Critical error during data fetching. Cannot continue signal generation.");
                return Enumerable.Empty<TradingSignal>();
            }

            // Log which symbols were excluded due to insufficient data
            var excludedSymbols = symbolList.Except(symbolDataMap.Keys).ToList();
            if (excludedSymbols.Any())
            {
                _logger.LogWarning("Excluded {Count} symbols due to insufficient data: {Symbols}",
                    excludedSymbols.Count, string.Join(", ", excludedSymbols));
            }

            // Log which symbols were successfully processed
            _logger.LogInformation("Successfully processed symbols: {Symbols}", string.Join(", ", symbolDataMap.Keys));

            // Step 3: Apply filters and generate signals in parallel
            var filteredSignals = await ApplyFiltersAndGenerateSignalsAsync(symbolDataMap);

            // Step 4: Calculate position sizing for qualified signals
            var enhancedSignals = await CalculatePositionSizingAsync(filteredSignals);

            // Step 5: Final ranking and selection
            var finalSignals = enhancedSignals
                .Where(s => s.PositionSizing?.Shares > 0)
                .OrderByDescending(s => s.PositionSizing?.ConfidenceScore ?? 0)
                .ThenByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .Select(s => new TradingSignal(s.Symbol, s.Price, s.Atr, s.SixMonthReturn))
                .ToList();

            totalStopwatch.Stop();

            _logger.LogInformation("Enhanced signal generation completed: {Count} signals from {Total} symbols in {ElapsedMs:F0}ms",
                finalSignals.Count, symbolList.Count, totalStopwatch.Elapsed.TotalMilliseconds);

            return finalSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in enhanced signal generation");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    /// <summary>
    /// Fetches market data for all symbols using intelligent batching and parallel async I/O with rate limiting
    /// </summary>
    private async Task<ConcurrentDictionary<string, List<IBar>>> FetchDataInParallelAsync(List<string> symbols, CancellationToken cancellationToken = default)
    {
        var dataStopwatch = Stopwatch.StartNew();
        var symbolDataMap = new ConcurrentDictionary<string, List<IBar>>();

        // INTELLIGENT BATCHING: Process symbols in smaller batches to improve success rate
        const int batchSize = 500; // Process 500 symbols at a time
        var batches = symbols.Chunk(batchSize).ToList();

        _logger.LogInformation("Processing {TotalSymbols} symbols in {BatchCount} batches of {BatchSize} symbols each",
            symbols.Count, batches.Count, batchSize);

        for (int batchIndex = 0; batchIndex < batches.Count; batchIndex++)
        {
            var batch = batches[batchIndex];
            var batchStopwatch = Stopwatch.StartNew();

            _logger.LogInformation("Processing batch {BatchIndex}/{TotalBatches} with {SymbolCount} symbols",
                batchIndex + 1, batches.Count, batch.Length);

            var fetchTasks = batch.Select(async symbol =>
        {
            var sw = Stopwatch.StartNew();

            // CIRCUIT BREAKER: Skip symbols that are consistently failing (only for data issues)
            if (_circuitBreaker.ShouldSkipSymbol(symbol))
            {
                _logger.LogDebug("Skipping {Symbol} due to circuit breaker (data issue)", symbol);
                return;
            }

            // ENHANCED: Add retry logic for rate gate acquisition
            var rateGateTimeout = TimeSpan.FromSeconds(45);
            const int maxRetries = 2;
            bool semaphoreAcquired = false;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                using var rateGateCts = new CancellationTokenSource(rateGateTimeout);
                using var combinedRateGateCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, rateGateCts.Token);

                try
                {
                    await _rateGate.WaitAsync(combinedRateGateCts.Token);
                    semaphoreAcquired = true;
                    break; // Successfully acquired semaphore
                }
                catch (OperationCanceledException) when (rateGateCts.Token.IsCancellationRequested)
                {
                    if (attempt == maxRetries)
                    {
                        // Final attempt failed - log warning and skip this symbol
                        _logger.LogWarning("Rate gate timeout for {Symbol} after {Timeout}s and {Attempts} attempts. " +
                                         "Current semaphore count: {Count}/80. " +
                                         "This indicates slow API responses or network issues. Skipping symbol.",
                                         symbol, rateGateTimeout.TotalSeconds, maxRetries, _rateGate.CurrentCount);
                        return; // Skip this symbol - don't fail the entire operation
                    }

                    // Wait before retry with exponential backoff
                    var retryDelay = TimeSpan.FromSeconds(Math.Pow(2, attempt - 1) * 5); // 5s, 10s
                    _logger.LogDebug("Rate gate timeout for {Symbol} on attempt {Attempt}/{MaxAttempts}. " +
                                   "Retrying in {Delay}s. Current semaphore count: {Count}/80",
                                   symbol, attempt, maxRetries, retryDelay.TotalSeconds, _rateGate.CurrentCount);

                    await Task.Delay(retryDelay, cancellationToken);
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    _logger.LogDebug("Rate gate wait cancelled for {Symbol} due to overall cancellation", symbol);
                    return; // Skip this symbol due to overall cancellation
                }
            }

            if (!semaphoreAcquired)
            {
                return; // Failed to acquire semaphore after all retries
            }

            try
            {
                // TEMPORARILY DISABLED: Check if already signaled today
                // var today = DateTime.UtcNow.Date;
                // var alreadySignaled = await _liveStateStore.WasSignaledAsync(symbol, today);

                // if (alreadySignaled)
                // {
                //     _logger.LogDebug("Skipping {Symbol} - already signaled today", symbol);
                //     return;
                // }

                _logger.LogDebug("Processing {Symbol} - fetching data with robust retry", symbol);

                // CRITICAL FIX: Use yesterday to avoid cache date range issues and ensure valid date ranges
                var endDate = DateTime.UtcNow.Date.AddDays(-1);
                var startDate = endDate.AddDays(-300);

                // ADDITIONAL SAFETY: Ensure startDate is always before endDate
                if (startDate >= endDate)
                {
                    _logger.LogWarning("Invalid date range for {Symbol}: start {Start:yyyy-MM-dd} >= end {End:yyyy-MM-dd}, adjusting",
                        symbol, startDate, endDate);
                    startDate = endDate.AddDays(-300);
                    if (startDate >= endDate)
                    {
                        startDate = endDate.AddDays(-1); // Emergency fallback
                    }
                }

                // ROBUST RETRY: Use retry service for reliable data fetching
                var response = await _retryService.ExecuteWithRetryAsync(symbol, async (ct) =>
                {
                    return await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate).WaitAsync(ct);
                }, cancellationToken);

                var bars = response.Items.ToList();
                _logger.LogDebug("Fetched {Count} bars for {Symbol}", bars.Count, symbol);

                if (bars.Count >= 200)
                {
                    symbolDataMap[symbol] = bars;
                    _logger.LogDebug("Added {Symbol} to symbol data map with {Count} bars", symbol, bars.Count);
                }
                else
                {
                    _logger.LogWarning("Insufficient bars for {Symbol}: {Count} (need 200+)", symbol, bars.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch data for {Symbol} after all retries", symbol);
                // Error already recorded by retry service
            }
            finally
            {
                _rateGate.Release();
                MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);

                // Log slow signals (but don't circuit break - retry service handles errors)
                if (sw.ElapsedMilliseconds > 500)
                {
                    _logger.LogWarning("Slow signal {Symbol} {Ms} ms", symbol, sw.ElapsedMilliseconds);
                }

                // Alert when semaphore utilization is high (less than 20 slots available)
                if (_rateGate.CurrentCount < 20)
                {
                    _logger.LogWarning("High semaphore utilization detected. Available slots: {Count}/80. " +
                                     "This may cause rate gate timeouts for other symbols.",
                                     _rateGate.CurrentCount);
                }
            }
            });

            // BATCH TIMEOUT: Use a shorter timeout per batch to allow for retries
            var batchTimeout = TimeSpan.FromMinutes(2); // 2 minutes per batch
            using var batchTimeoutCts = new CancellationTokenSource(batchTimeout);
            using var combinedBatchCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, batchTimeoutCts.Token);

            try
            {
                await Task.WhenAll(fetchTasks).WaitAsync(combinedBatchCts.Token);
                batchStopwatch.Stop();

                var batchSuccessCount = batch.Count(symbol => symbolDataMap.ContainsKey(symbol));
                _logger.LogInformation("Batch {BatchIndex}/{TotalBatches} completed in {ElapsedMs:F0}ms. Success rate: {SuccessCount}/{TotalCount} ({SuccessRate:P1})",
                    batchIndex + 1, batches.Count, batchStopwatch.Elapsed.TotalMilliseconds,
                    batchSuccessCount, batch.Length, (double)batchSuccessCount / batch.Length);
            }
            catch (OperationCanceledException) when (batchTimeoutCts.Token.IsCancellationRequested)
            {
                batchStopwatch.Stop();
                var batchSuccessCount = batch.Count(symbol => symbolDataMap.ContainsKey(symbol));
                _logger.LogWarning("Batch {BatchIndex}/{TotalBatches} timed out after {Timeout}. Success rate: {SuccessCount}/{TotalCount} ({SuccessRate:P1}). Continuing to next batch.",
                    batchIndex + 1, batches.Count, batchTimeout,
                    batchSuccessCount, batch.Length, (double)batchSuccessCount / batch.Length);
            }
            catch (Exception ex)
            {
                batchStopwatch.Stop();
                _logger.LogError(ex, "Error processing batch {BatchIndex}/{TotalBatches}. Continuing to next batch.",
                    batchIndex + 1, batches.Count);
            }

            // Add a small delay between batches to avoid overwhelming the APIs
            if (batchIndex < batches.Count - 1)
            {
                await Task.Delay(TimeSpan.FromSeconds(2), cancellationToken);
            }
        }

        // RETRY MECHANISM: Attempt to fetch failed symbols one more time
        var failedSymbols = symbols.Where(s => !symbolDataMap.ContainsKey(s)).ToList();
        if (failedSymbols.Any() && failedSymbols.Count <= 100) // Only retry if we have a reasonable number of failures
        {
            _logger.LogInformation("Retrying {FailedCount} failed symbols", failedSymbols.Count);

            var retryTasks = failedSymbols.Select(async symbol =>
            {
                try
                {
                    await _rateGate.WaitAsync(TimeSpan.FromSeconds(30), cancellationToken);
                    try
                    {
                        var endDate = DateTime.UtcNow.Date.AddDays(-1);
                        var startDate = endDate.AddDays(-300);

                        using var retryCts = new CancellationTokenSource(TimeSpan.FromSeconds(45));
                        using var combinedRetryCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, retryCts.Token);

                        var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate).WaitAsync(combinedRetryCts.Token);
                        var bars = response.Items.ToList();

                        if (bars.Count >= 200)
                        {
                            symbolDataMap[symbol] = bars;
                            _logger.LogDebug("Retry successful for {Symbol}", symbol);
                        }
                    }
                    finally
                    {
                        _rateGate.Release();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Retry failed for {Symbol}", symbol);
                }
            });

            try
            {
                using var retryCts = new CancellationTokenSource(TimeSpan.FromMinutes(3));
                using var combinedRetryCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, retryCts.Token);
                await Task.WhenAll(retryTasks).WaitAsync(combinedRetryCts.Token);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Retry operation timed out after 3 minutes");
            }

            var retrySuccessCount = failedSymbols.Count(s => symbolDataMap.ContainsKey(s));
            _logger.LogInformation("Retry completed. Additional {RetrySuccess}/{RetryTotal} symbols recovered",
                retrySuccessCount, failedSymbols.Count);
        }

        dataStopwatch.Stop();
        var totalSuccessCount = symbolDataMap.Count;
        var overallSuccessRate = (double)totalSuccessCount / symbols.Count;

        _logger.LogInformation("Data fetching completed in {ElapsedMs:F0}ms. Overall success rate: {SuccessCount}/{TotalCount} ({SuccessRate:P1})",
            dataStopwatch.Elapsed.TotalMilliseconds, totalSuccessCount, symbols.Count, overallSuccessRate);

        // Only throw if we have a very low success rate (less than 10%)
        if (overallSuccessRate < 0.1)
        {
            throw new TimeoutException($"Market data fetch operation failed with very low success rate: {totalSuccessCount}/{symbols.Count} ({overallSuccessRate:P1})");
        }

        return symbolDataMap;
    }

    /// <summary>
    /// Applies momentum and volatility filters, then generates signals
    /// </summary>
    private async Task<List<EnhancedTradingSignalV2>> ApplyFiltersAndGenerateSignalsAsync(
        ConcurrentDictionary<string, List<IBar>> symbolDataMap)
    {
        var filterStopwatch = Stopwatch.StartNew();
        var signals = new ConcurrentBag<EnhancedTradingSignalV2>();
        var processedCount = 0;

        await Task.Run(() =>
        {
            Parallel.ForEach(symbolDataMap, _parallelOptions, kvp =>
            {
                var symbol = kvp.Key;
                var bars = kvp.Value;

                try
                {
                    // Apply momentum filter
                    var momentumEligible = _momentumFilter.IsEligible(symbol, bars);
                    if (!momentumEligible)
                    {
                        _logger.LogDebug("Momentum filter rejected {Symbol}", symbol);
                        return;
                    }

                    // Apply volatility filter (async call in sync context - not ideal but necessary for Parallel.ForEach)
                    var volatilityTask = _volatilityFilter.IsEligibleAsync(symbol, bars);
                    volatilityTask.Wait();
                    var volatilityEligible = volatilityTask.Result;

                    if (!volatilityEligible)
                    {
                        _logger.LogDebug("Volatility filter rejected {Symbol}", symbol);
                        return;
                    }

                    // Generate signal with enhanced data
                    var signal = GenerateEnhancedSignal(symbol, bars);
                    if (signal != null)
                    {
                        signals.Add(signal);
                    }

                    var count = Interlocked.Increment(ref processedCount);
                    if (count % 25 == 0)
                    {
                        _logger.LogDebug("Processed {Count}/{Total} symbols through filters",
                            count, symbolDataMap.Count);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process {Symbol} through filters", symbol);
                }
            });
        });

        filterStopwatch.Stop();

        _logger.LogDebug("Filter processing completed in {ElapsedMs:F0}ms, {Count} signals passed filters",
            filterStopwatch.Elapsed.TotalMilliseconds, signals.Count);

        return signals.ToList();
    }

    /// <summary>
    /// Generates enhanced trading signal with additional analysis
    /// </summary>
    private EnhancedTradingSignalV2? GenerateEnhancedSignal(string symbol, List<IBar> bars)
    {
        try
        {
            var currentPrice = bars.Last().Close;

            // Calculate technical indicators
            var sma50 = (decimal)bars.GetSma50();
            var sma200 = (decimal)bars.GetSma200();
            var atr14 = (decimal)bars.GetAtr14();
            var sixMonthReturn = (decimal)bars.GetTotalReturn(126);

            // Enhanced analysis
            var momentumAnalysis = _momentumFilter.AnalyzeMomentum(symbol, bars);
            var volatilityAnalysis = _volatilityFilter.AnalyzeSymbolVolatility(symbol, bars);

            // Basic trend filter
            if (currentPrice <= sma50 || currentPrice <= sma200)
                return null;

            return new EnhancedTradingSignalV2(
                symbol,
                currentPrice,
                atr14,
                sixMonthReturn,
                momentumAnalysis,
                volatilityAnalysis,
                null // Position sizing will be calculated later
            );
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error generating enhanced signal for {Symbol}", symbol);
            return null;
        }
    }

    /// <summary>
    /// Calculates position sizing for qualified signals
    /// </summary>
    private async Task<List<EnhancedTradingSignalV2>> CalculatePositionSizingAsync(List<EnhancedTradingSignalV2> signals)
    {
        var sizingStopwatch = Stopwatch.StartNew();

        try
        {
            // Get account equity for position sizing
            var account = await _marketDataService.GetAccountAsync();
            var accountEquity = account.Equity;

            var sizingTasks = signals.Select(async signal =>
            {
                try
                {
                    var baseSignal = new TradingSignal(signal.Symbol, signal.Price, signal.Atr, signal.SixMonthReturn);
                    var positionSizing = await _positionSizer.CalculateSizingAsync(baseSignal, accountEquity ?? 0);

                    return signal with { PositionSizing = positionSizing };
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to calculate position sizing for {Symbol}", signal.Symbol);
                    return signal; // Return without position sizing
                }
            });

            var results = await Task.WhenAll(sizingTasks);

            sizingStopwatch.Stop();
            _logger.LogDebug("Position sizing completed in {ElapsedMs:F0}ms", sizingStopwatch.Elapsed.TotalMilliseconds);

            return results.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in position sizing calculation");
            return signals; // Return signals without position sizing
        }
    }

    public void Dispose()
    {
        _rateGate?.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Enhanced trading signal with additional analysis and position sizing
/// </summary>
public record EnhancedTradingSignalV2(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    MomentumAnalysis MomentumAnalysis,
    SymbolVolatilityAnalysis VolatilityAnalysis,
    DynamicPositionSizing? PositionSizing
);
