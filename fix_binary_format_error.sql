-- Fix for PostgreSQL binary format error (22P03) in CachedStockBars table
-- This script updates the Volume and TradeCount column types to match the C# model changes
-- Run this script on your PostgreSQL database to resolve the binary format issues

-- ============================================================================
-- BACKUP EXISTING DATA (RECOMMENDED)
-- ============================================================================
-- Uncomment the following line to create a backup before making changes:
-- CREATE TABLE "CachedStockBars_backup" AS SELECT * FROM "CachedStockBars";

-- ============================================================================
-- FIX COLUMN TYPE MISMATCHES
-- ============================================================================

-- 1. Update Volume column from bigint to decimal(18,0) to match EF configuration
-- This fixes the mismatch between C# decimal type and PostgreSQL bigint
ALTER TABLE "CachedStockBars" 
ALTER COLUMN "Volume" TYPE decimal(18,0) USING "Volume"::decimal(18,0);

-- 2. Update TradeCount column from bigint to bigint (nullable)
-- This ensures proper handling of nullable long values from C#
-- Note: PostgreSQL bigint maps to C# long, which is what we changed TradeCount to
ALTER TABLE "CachedStockBars" 
ALTER COLUMN "TradeCount" TYPE bigint USING "TradeCount"::bigint;

-- ============================================================================
-- VERIFY CHANGES
-- ============================================================================

-- Check the updated column types
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_name = 'CachedStockBars' 
    AND column_name IN ('Volume', 'TradeCount')
ORDER BY column_name;

-- ============================================================================
-- CLEAN UP PROBLEMATIC DATA (OPTIONAL)
-- ============================================================================

-- If you want to clean up the specific symbol that was causing issues:
-- DELETE FROM "CachedStockBars" WHERE "Symbol" = 'AMBP';
-- DELETE FROM "StockCacheMetadata" WHERE "Symbol" = 'AMBP';

-- ============================================================================
-- NOTES
-- ============================================================================

/*
This script fixes the PostgreSQL binary format error (22P03) by:

1. Changing Volume from bigint to decimal(18,0) to match the C# decimal type
   and Entity Framework configuration

2. Ensuring TradeCount is properly configured as bigint (nullable) to match
   the C# long? type

The binary format error occurred because:
- C# model defined Volume as long, but EF configured it as decimal(18,0)
- C# model defined TradeCount as ulong?, but PostgreSQL doesn't support ulong
- The mismatch caused PostgreSQL to receive data in an unexpected binary format

After running this script, the "AMBP" symbol and other symbols should cache
successfully without the binary format error.
*/
