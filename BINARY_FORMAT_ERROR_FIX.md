# PostgreSQL Binary Format Error Fix (22P03)

## Problem Description

The application was experiencing PostgreSQL binary format errors when caching bar data for certain symbols like "AMBP":

```
[Error] Database error caching bars for "AMBP" "Minute": "22P03": "incorrect binary data format"
```

## Root Cause Analysis

The error was caused by data type mismatches between the C# model, Entity Framework configuration, and PostgreSQL expectations:

### 1. Volume Field Mismatch
- **C# Model**: `public long Volume { get; set; }`
- **EF Configuration**: `entity.Property(e => e.Volume).HasPrecision(18, 0);` (treats as decimal)
- **IBar Interface**: `public decimal Volume { get; set; }`
- **Issue**: Casting `decimal` to `long` and then trying to store as `decimal(18,0)` in PostgreSQL

### 2. TradeCount Field Issue
- **C# Model**: `public ulong? TradeCount { get; set; }`
- **PostgreSQL**: No native `ulong` type support
- **Issue**: PostgreSQL couldn't handle the `ulong?` type in binary format

## Solution Implemented

### 1. Updated CachedStockBar Model

**Before:**
```csharp
[Required]
public long Volume { get; set; }

public ulong? TradeCount { get; set; }
```

**After:**
```csharp
[Required]
[Column(TypeName = "decimal(18,0)")]
public decimal Volume { get; set; }

public long? TradeCount { get; set; }
```

### 2. Updated Entity Framework Configuration

**Before:**
```csharp
entity.Property(e => e.Volume).HasPrecision(18, 0);
// No TradeCount configuration
```

**After:**
```csharp
// Volume precision is now defined in the model using Column attribute
entity.Property(e => e.TradeCount).HasColumnType("bigint");
```

### 3. Updated FromIBar Method

**Before:**
```csharp
cachedBar.Volume = (long)bar.Volume;
cachedBar.TradeCount = bar.TradeCount > 0 ? bar.TradeCount : null;
```

**After:**
```csharp
cachedBar.Volume = bar.Volume;
cachedBar.TradeCount = bar.TradeCount > 0 ? (long?)bar.TradeCount : null;
```

### 4. Updated Supporting Classes

- Updated `BarData` classes in both `CacheModels.cs` and `CacheCompressionService.cs`
- Updated `CachedBarWrapper` to handle type conversions properly

## Database Schema Update Required

Run the provided SQL script `fix_binary_format_error.sql` to update your PostgreSQL database:

```sql
-- Update Volume column type
ALTER TABLE "CachedStockBars" 
ALTER COLUMN "Volume" TYPE decimal(18,0) USING "Volume"::decimal(18,0);

-- Update TradeCount column type  
ALTER TABLE "CachedStockBars" 
ALTER COLUMN "TradeCount" TYPE bigint USING "TradeCount"::bigint;
```

## Files Modified

1. `SmaTrendFollower.Console/Models/CacheModels.cs`
   - Updated `CachedStockBar` properties
   - Updated `FromIBar` method
   - Updated `CachedBarWrapper` class
   - Updated private `BarData` class

2. `SmaTrendFollower.Console/Data/StockBarCacheDbContext.cs`
   - Updated Entity Framework configuration

3. `SmaTrendFollower.Console/Services/CacheCompressionService.cs`
   - Updated private `BarData` class

## Testing

Use the provided test file `test_binary_format_fix.cs` to verify the fix works:

```bash
dotnet run --project test_binary_format_fix.cs
```

The test will:
1. Create test bar data for the "AMBP" symbol
2. Attempt to cache the bars (previously failed)
3. Verify data integrity
4. Test edge cases like null TradeCount

## Expected Results

After applying this fix:

1. ✅ No more "22P03: incorrect binary data format" errors
2. ✅ Symbols like "AMBP" will cache successfully
3. ✅ Volume data stored as `decimal(18,0)` matching IBar interface
4. ✅ TradeCount data stored as `bigint` (nullable) compatible with PostgreSQL
5. ✅ Proper type conversions between C# and PostgreSQL

## Prevention

This issue was caused by:
- Inconsistent data types between model and EF configuration
- Using `ulong` which isn't PostgreSQL-compatible
- Casting between incompatible types

To prevent similar issues:
- Ensure C# model types match EF configuration
- Use PostgreSQL-compatible types (`long` instead of `ulong`)
- Test with actual data that matches API responses
- Use explicit column type attributes when needed
