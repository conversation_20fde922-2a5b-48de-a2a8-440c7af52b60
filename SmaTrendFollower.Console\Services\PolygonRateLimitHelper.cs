using Microsoft.Extensions.Logging;
using <PERSON>;
using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Polygon rate limiting helper
/// Extends IRetryService for unified retry interface
/// </summary>
public interface IPolygonRateLimitHelper : IRetryService
{
    /// <summary>
    /// Executes a Polygon HTTP API call with rate limiting and retry logic
    /// </summary>
    Task<HttpResponseMessage> ExecuteAsync(Func<Task<HttpResponseMessage>> httpCall, string operationName = "Unknown");

    /// <summary>
    /// Executes a Polygon API call that returns a specific type with rate limiting and retry logic
    /// </summary>
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown");
}

/// <summary>
/// Rate limiting helper for Polygon API calls with exponential back-off
/// Enhanced for Developer subscription: ~100 requests/second with intelligent batching
/// Now implements IRetryService for unified retry interface
/// </summary>
public sealed class PolygonRateLimitHelper : IPolygonRateLimitHelper
{
    private readonly ILogger<PolygonRateLimitHelper> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;
    private readonly IPolygonRetryService _retryService;
    private int _requestCount;
    private DateTime _windowStart;

    // Enhanced rate limits for Developer subscription
    private const int DeveloperRateLimit = 100; // requests per second
    private const int BurstLimit = 150; // allow short bursts

    public PolygonRateLimitHelper(ILogger<PolygonRateLimitHelper> logger, IPolygonRetryService? retryService = null)
    {
        _logger = logger;
        _semaphore = new SemaphoreSlim(5, 5); // Allow 5 concurrent requests to match 5 req/sec rate limit
        _requestCount = 0;
        _windowStart = DateTime.UtcNow;

        // Use injected retry service or create a new one
        _retryService = retryService ?? CreateDefaultPolygonRetryService(logger);

        // Reset counter every second
        _resetTimer = new Timer(ResetCounter, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        // Enhanced retry policy with circuit breaker pattern
        var circuitBreakerPolicy = Policy
            .Handle<HttpRequestException>()
            .Or<TaskCanceledException>()
            .OrResult<HttpResponseMessage>(r =>
                r.StatusCode == HttpStatusCode.ServiceUnavailable ||
                r.StatusCode == HttpStatusCode.BadGateway ||
                r.StatusCode == HttpStatusCode.GatewayTimeout)
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 3,
                durationOfBreak: TimeSpan.FromMinutes(1),
                onBreak: (exception, duration) =>
                {
                    _logger.LogError("Polygon API circuit breaker opened for {Duration}ms due to: {Exception}",
                        duration.TotalMilliseconds, exception.Exception?.Message ?? "Multiple failures");
                },
                onReset: () =>
                {
                    _logger.LogInformation("Polygon API circuit breaker reset - service recovered");
                });

        // Exponential back-off retry policy for HTTP responses
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r =>
                r.StatusCode == HttpStatusCode.TooManyRequests ||
                r.StatusCode == HttpStatusCode.ServiceUnavailable ||
                r.StatusCode == HttpStatusCode.RequestTimeout ||
                r.StatusCode == HttpStatusCode.InternalServerError)
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polygon rate limit retry {RetryCount}/5 after {Delay}ms. Status: {Status}",
                        retryCount, timespan.TotalMilliseconds,
                        outcome.Result?.StatusCode.ToString() ?? outcome.Exception?.Message ?? "Unknown");
                });

        // Combine circuit breaker and retry policies
        _retryPolicy = Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);
    }

    /// <summary>
    /// Executes a Polygon HTTP API call with rate limiting and retry logic
    /// </summary>
    public async Task<HttpResponseMessage> ExecuteAsync(Func<Task<HttpResponseMessage>> httpCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment request counter
            Interlocked.Increment(ref _requestCount);

            _logger.LogDebug("Executing Polygon API call: {Operation} (Request {Count}/{DeveloperRateLimit} in current window)",
                operationName, _requestCount, DeveloperRateLimit);

            // Execute with retry policy
            return await _retryPolicy.ExecuteAsync(async () =>
            {
                var response = await httpCall();
                
                if (response.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    _logger.LogWarning("Polygon rate limit hit for {Operation}, will retry with exponential back-off", operationName);
                }
                
                return response;
            });
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Executes a Polygon API call that returns a specific type with rate limiting and retry logic
    /// </summary>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment request counter
            Interlocked.Increment(ref _requestCount);

            _logger.LogDebug("Executing Polygon API call: {Operation} (Request {Count}/{DeveloperRateLimit} in current window)",
                operationName, _requestCount, DeveloperRateLimit);

            // For non-HTTP calls, use a simpler retry policy
            var simpleRetryPolicy = Policy
                .Handle<Exception>(ex => 
                    ex.Message.Contains("TooManyRequests") || 
                    ex.Message.Contains("429") ||
                    ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
                .WaitAndRetryAsync(
                    retryCount: 5,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                    onRetry: (exception, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Polygon rate limit retry {RetryCount}/5 after {Delay}ms. Exception: {Exception}",
                            retryCount, timespan.TotalMilliseconds, exception.Message);
                    });

            return await simpleRetryPolicy.ExecuteAsync(apiCall);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task WaitIfNecessary()
    {
        // Enhanced rate limiting for Developer subscription
        // Allow bursts up to 150 req/sec, then throttle to 100 req/sec
        if (_requestCount >= BurstLimit)
        {
            var delay = TimeSpan.FromMilliseconds(50 + Random.Shared.Next(0, 25));
            _logger.LogDebug("At Polygon burst limit ({Count}/{BurstLimit}), adding {Delay}ms delay",
                _requestCount, BurstLimit, delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
        // If approaching burst limit, add small delay
        else if (_requestCount >= DeveloperRateLimit)
        {
            var delay = TimeSpan.FromMilliseconds(10 + Random.Shared.Next(0, 10));
            _logger.LogDebug("Approaching Polygon rate limit ({Count}/{DeveloperRateLimit}), adding {Delay}ms delay",
                _requestCount, DeveloperRateLimit, delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
    }

    private void ResetCounter(object? state)
    {
        var oldCount = Interlocked.Exchange(ref _requestCount, 0);
        _windowStart = DateTime.UtcNow;
        
        if (oldCount > 0)
        {
            _logger.LogDebug("Reset Polygon rate limit counter. Previous window: {Count}/5 requests", oldCount);
        }
    }

    // IRetryService implementation - delegate to the retry service
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        return await _retryService.ExecuteAsync(operation, operationName, cancellationToken);
    }

    public async Task ExecuteAsync(Func<Task> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        await _retryService.ExecuteAsync(operation, operationName, cancellationToken);
    }

    public async Task<HttpResponseMessage> ExecuteHttpAsync(Func<Task<HttpResponseMessage>> httpOperation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        return await _retryService.ExecuteHttpAsync(httpOperation, operationName, cancellationToken);
    }

    public RetryConfiguration GetConfiguration() => _retryService.GetConfiguration();

    public void UpdateConfiguration(RetryConfiguration configuration) => _retryService.UpdateConfiguration(configuration);

    public RateLimitStatistics GetStatistics() => _retryService.GetStatistics();

    public void ResetStatistics() => _retryService.ResetStatistics();

    private static IPolygonRetryService CreateDefaultPolygonRetryService(ILogger logger)
    {
        // Create a logger adapter for the retry service
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var retryLogger = loggerFactory.CreateLogger<PolygonRetryService>();
        return new PolygonRetryService(retryLogger);
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
        _semaphore?.Dispose();
        if (_retryService is IDisposable disposableRetryService)
        {
            disposableRetryService.Dispose();
        }
    }
}
