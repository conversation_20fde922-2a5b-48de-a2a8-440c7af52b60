using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;
using Microsoft.Extensions.Logging;

/// <summary>
/// Simple utility to test PostgreSQL connection and create database schema
/// </summary>
public class PostgreSQLTest
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🐘 Testing PostgreSQL Connection...");
        Console.WriteLine("=" * 50);
        
        var connectionString = "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";
        
        try
        {
            // Test StockBarCacheDbContext
            Console.WriteLine("📊 Testing StockBarCacheDbContext...");
            await TestStockBarCacheDbContext(connectionString);
            
            // Test IndexCacheDbContext  
            Console.WriteLine("\n📈 Testing IndexCacheDbContext...");
            await TestIndexCacheDbContext(connectionString);
            
            // Test MLFeaturesDbContext
            Console.WriteLine("\n🤖 Testing MLFeaturesDbContext...");
            await TestMLFeaturesDbContext(connectionString);
            
            Console.WriteLine("\n✅ All PostgreSQL tests passed successfully!");
            Console.WriteLine("🎉 Database schema created and ready for use!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ PostgreSQL test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            Environment.Exit(1);
        }
    }
    
    private static async Task TestStockBarCacheDbContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseNpgsql(connectionString)
            .EnableSensitiveDataLogging(true)
            .LogTo(Console.WriteLine, LogLevel.Information)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var context = new StockBarCacheDbContext(options);
        
        // Test connection
        Console.WriteLine("  🔗 Testing connection...");
        var canConnect = await context.Database.CanConnectAsync();
        if (!canConnect)
        {
            throw new InvalidOperationException("Cannot connect to PostgreSQL database");
        }
        Console.WriteLine("  ✅ Connection successful");
        
        // Create database schema
        Console.WriteLine("  🏗️ Creating database schema...");
        await context.Database.EnsureCreatedAsync();
        Console.WriteLine("  ✅ Schema created successfully");
        
        // Test basic operations
        Console.WriteLine("  📝 Testing basic operations...");
        var testBar = new SmaTrendFollower.Models.CachedStockBar
        {
            Symbol = "TEST",
            TimeFrame = "Day",
            Timestamp = DateTime.UtcNow,
            Open = 100.0m,
            High = 105.0m,
            Low = 99.0m,
            Close = 102.0m,
            Volume = 1000000,
            CachedAt = DateTime.UtcNow
        };
        
        context.CachedStockBars.Add(testBar);
        await context.SaveChangesAsync();
        Console.WriteLine("  ✅ Write test successful");
        
        var retrievedBar = await context.CachedStockBars.FirstOrDefaultAsync(b => b.Symbol == "TEST");
        if (retrievedBar == null)
        {
            throw new InvalidOperationException("Failed to retrieve test data");
        }
        Console.WriteLine("  ✅ Read test successful");
        
        // Cleanup
        context.CachedStockBars.Remove(retrievedBar);
        await context.SaveChangesAsync();
        Console.WriteLine("  ✅ Delete test successful");
    }
    
    private static async Task TestIndexCacheDbContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<IndexCacheDbContext>()
            .UseNpgsql(connectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var context = new IndexCacheDbContext(options);
        
        Console.WriteLine("  🔗 Testing connection...");
        var canConnect = await context.Database.CanConnectAsync();
        if (!canConnect)
        {
            throw new InvalidOperationException("Cannot connect to PostgreSQL database");
        }
        Console.WriteLine("  ✅ Connection successful");
        
        Console.WriteLine("  🏗️ Creating database schema...");
        await context.Database.EnsureCreatedAsync();
        Console.WriteLine("  ✅ Schema created successfully");
    }
    
    private static async Task TestMLFeaturesDbContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<SmaTrendFollower.Data.MLFeaturesDbContext>()
            .UseNpgsql(connectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var context = new SmaTrendFollower.Data.MLFeaturesDbContext(options);
        
        Console.WriteLine("  🔗 Testing connection...");
        var canConnect = await context.Database.CanConnectAsync();
        if (!canConnect)
        {
            throw new InvalidOperationException("Cannot connect to PostgreSQL database");
        }
        Console.WriteLine("  ✅ Connection successful");
        
        Console.WriteLine("  🏗️ Creating database schema...");
        await context.Database.EnsureCreatedAsync();
        Console.WriteLine("  ✅ Schema created successfully");
    }
}
