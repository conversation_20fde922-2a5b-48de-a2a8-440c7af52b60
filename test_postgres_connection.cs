using Npgsql;
using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;
using System;
using System.Threading.Tasks;

/// <summary>
/// Simple utility to test PostgreSQL connection and create database schema
/// </summary>
public static class PostgreSQLConnectionTest
{
    private const string ConnectionString = "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";
    
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 Testing PostgreSQL Connection...");
        Console.WriteLine("=" * 50);
        
        try
        {
            // Test 1: Basic connection
            await TestBasicConnection();
            
            // Test 2: Create database schema
            await CreateDatabaseSchema();
            
            // Test 3: Test read/write operations
            await TestReadWriteOperations();
            
            Console.WriteLine("✅ All PostgreSQL tests passed!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ PostgreSQL test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            Environment.Exit(1);
        }
    }
    
    private static async Task TestBasicConnection()
    {
        Console.WriteLine("🔌 Testing basic PostgreSQL connection...");
        
        using var connection = new NpgsqlConnection(ConnectionString);
        await connection.OpenAsync();
        
        using var command = new NpgsqlCommand("SELECT version()", connection);
        var version = await command.ExecuteScalarAsync();
        
        Console.WriteLine($"✅ Connected to PostgreSQL: {version}");
    }
    
    private static async Task CreateDatabaseSchema()
    {
        Console.WriteLine("🏗️ Creating database schema...");
        
        // Create StockBarCacheDbContext schema
        var stockOptions = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseNpgsql(ConnectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var stockContext = new StockBarCacheDbContext(stockOptions);
        await stockContext.Database.EnsureCreatedAsync();
        Console.WriteLine("✅ StockBarCache schema created");
        
        // Create IndexCacheDbContext schema
        var indexOptions = new DbContextOptionsBuilder<IndexCacheDbContext>()
            .UseNpgsql(ConnectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var indexContext = new IndexCacheDbContext(indexOptions);
        await indexContext.Database.EnsureCreatedAsync();
        Console.WriteLine("✅ IndexCache schema created");
        
        // Create MLFeaturesDbContext schema
        var mlOptions = new DbContextOptionsBuilder<SmaTrendFollower.Data.MLFeaturesDbContext>()
            .UseNpgsql(ConnectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var mlContext = new SmaTrendFollower.Data.MLFeaturesDbContext(mlOptions);
        await mlContext.Database.EnsureCreatedAsync();
        Console.WriteLine("✅ MLFeatures schema created");
    }
    
    private static async Task TestReadWriteOperations()
    {
        Console.WriteLine("📝 Testing read/write operations...");
        
        var options = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseNpgsql(ConnectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;
            
        using var context = new StockBarCacheDbContext(options);
        
        // Test write operation
        var testBar = new SmaTrendFollower.Models.CachedStockBar
        {
            Symbol = "TEST",
            TimeFrame = "Day",
            Timestamp = DateTime.UtcNow,
            Open = 100.0m,
            High = 105.0m,
            Low = 95.0m,
            Close = 102.0m,
            Volume = 1000000,
            CachedAt = DateTime.UtcNow
        };
        
        context.CachedStockBars.Add(testBar);
        await context.SaveChangesAsync();
        Console.WriteLine("✅ Write operation successful");
        
        // Test read operation
        var readBar = await context.CachedStockBars
            .FirstOrDefaultAsync(b => b.Symbol == "TEST");
            
        if (readBar != null)
        {
            Console.WriteLine($"✅ Read operation successful: {readBar.Symbol} @ ${readBar.Close}");
        }
        else
        {
            throw new Exception("Failed to read test data");
        }
        
        // Cleanup
        context.CachedStockBars.Remove(readBar);
        await context.SaveChangesAsync();
        Console.WriteLine("✅ Cleanup successful");
    }
}
