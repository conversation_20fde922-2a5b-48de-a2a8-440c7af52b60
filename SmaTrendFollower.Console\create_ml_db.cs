using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;

namespace SmaTrendFollower.Console;

/// <summary>
/// Simple utility to create the ML features database
/// </summary>
public static class CreateMLDatabase
{
    public static async Task CreateAsync()
    {
        System.Console.WriteLine("Creating ML Features database...");

        var baseConnectionString = "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";

        // Use standardized optimized connection string configuration
        var optimizedConnectionString = SmaTrendFollower.Services.DatabaseConfigurationHelper.CreateOptimizedBuilder(baseConnectionString).ConnectionString;

        var options = new DbContextOptionsBuilder<MLFeaturesDbContext>()
            .UseNpgsql(optimizedConnectionString)
            .ConfigureWarnings(warnings =>
            {
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
            })
            .Options;

        using var context = new MLFeaturesDbContext(options);

        // Create the database and tables
        await context.Database.EnsureCreatedAsync();

        System.Console.WriteLine("✅ ML Features database created successfully!");
        System.Console.WriteLine("Database: tradingbot_db (PostgreSQL)");
        System.Console.WriteLine("Tables created: Features, FillsLog");
    }
}
