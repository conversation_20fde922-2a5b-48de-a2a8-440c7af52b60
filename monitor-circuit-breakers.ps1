#!/usr/bin/env pwsh

# Monitor circuit breaker status and slow signals
Write-Host "Circuit Breaker and Timeout Monitoring" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# Function to check recent logs for slow signals and failures
function Check-RecentLogs {
    param(
        [string]$LogPath = "logs",
        [int]$LastMinutes = 30
    )
    
    $cutoffTime = (Get-Date).AddMinutes(-$LastMinutes)
    $logFiles = Get-ChildItem -Path $LogPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
    
    Write-Host "`nAnalyzing logs from last $LastMinutes minutes..." -ForegroundColor Yellow
    
    $slowSignals = @()
    $failedFetches = @()
    $circuitBreakerEvents = @()
    
    foreach ($logFile in $logFiles) {
        if ($logFile.LastWriteTime -gt $cutoffTime) {
            $content = Get-Content $logFile.FullName -ErrorAction SilentlyContinue
            
            foreach ($line in $content) {
                if ($line -match "Slow signal (\w+) (\d+) ms") {
                    $symbol = $matches[1]
                    $duration = [int]$matches[2]
                    $slowSignals += [PSCustomObject]@{
                        Symbol = $symbol
                        Duration = $duration
                        LogFile = $logFile.Name
                        Line = $line
                    }
                }
                
                if ($line -match "Failed to fetch data for `"(\w+)`"") {
                    $symbol = $matches[1]
                    $failedFetches += [PSCustomObject]@{
                        Symbol = $symbol
                        LogFile = $logFile.Name
                        Line = $line
                    }
                }
                
                if ($line -match "Circuit breaker.*(\w+)") {
                    $circuitBreakerEvents += [PSCustomObject]@{
                        LogFile = $logFile.Name
                        Line = $line
                    }
                }
            }
        }
    }
    
    # Report slow signals
    if ($slowSignals.Count -gt 0) {
        Write-Host "`n🐌 Slow Signals Detected:" -ForegroundColor Red
        $slowSignals | Sort-Object Duration -Descending | ForEach-Object {
            $color = if ($_.Duration -gt 30000) { "Red" } elseif ($_.Duration -gt 10000) { "Yellow" } else { "Cyan" }
            Write-Host "  $($_.Symbol): $($_.Duration)ms" -ForegroundColor $color
        }
        
        # Show top problematic symbols
        $topSlow = $slowSignals | Group-Object Symbol | Sort-Object Count -Descending | Select-Object -First 5
        Write-Host "`n🔥 Most Problematic Symbols:" -ForegroundColor Yellow
        $topSlow | ForEach-Object {
            $avgDuration = ($_.Group | Measure-Object Duration -Average).Average
            Write-Host "  $($_.Name): $($_.Count) slow signals, avg $([int]$avgDuration)ms" -ForegroundColor Red
        }
    } else {
        Write-Host "`n✅ No slow signals detected in recent logs" -ForegroundColor Green
    }
    
    # Report failed fetches
    if ($failedFetches.Count -gt 0) {
        Write-Host "`n❌ Failed Data Fetches:" -ForegroundColor Red
        $failedFetches | Group-Object Symbol | Sort-Object Count -Descending | ForEach-Object {
            Write-Host "  $($_.Name): $($_.Count) failures" -ForegroundColor Red
        }
    } else {
        Write-Host "`n✅ No failed data fetches detected in recent logs" -ForegroundColor Green
    }
    
    # Report circuit breaker events
    if ($circuitBreakerEvents.Count -gt 0) {
        Write-Host "`n⚡ Circuit Breaker Events:" -ForegroundColor Yellow
        $circuitBreakerEvents | ForEach-Object {
            Write-Host "  $($_.Line)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "`n✅ No circuit breaker events detected" -ForegroundColor Green
    }
}

# Function to show current timeout configuration
function Show-TimeoutConfig {
    Write-Host "`n⏱️  Current Timeout Configuration:" -ForegroundColor Cyan
    
    if (Test-Path "SmaTrendFollower.Console/appsettings.timeouts.json") {
        $config = Get-Content "SmaTrendFollower.Console/appsettings.timeouts.json" | ConvertFrom-Json
        Write-Host "  HTTP Standard Request: $($config.Timeouts.Http.StandardRequest)" -ForegroundColor White
        Write-Host "  Market Data Symbol Timeout: $($config.Timeouts.MarketData.SymbolTimeout)" -ForegroundColor White
        Write-Host "  Signal Generation: $($config.Timeouts.Trading.SignalGeneration)" -ForegroundColor White
        Write-Host "  Retry Max Total Time: $($config.Timeouts.Retry.MaxTotalTime)" -ForegroundColor White
    } else {
        Write-Host "  ❌ Timeout configuration file not found" -ForegroundColor Red
    }
}

# Function to provide recommendations
function Show-Recommendations {
    Write-Host "`n💡 Recommendations:" -ForegroundColor Green
    Write-Host "  1. Monitor for symbols consistently taking >30 seconds" -ForegroundColor White
    Write-Host "  2. Check network connectivity if many symbols are failing" -ForegroundColor White
    Write-Host "  3. Consider reducing universe size if too many timeouts occur" -ForegroundColor White
    Write-Host "  4. Circuit breaker will automatically skip problematic symbols" -ForegroundColor White
    Write-Host "  5. Symbols like 'C' should now timeout at 30s instead of 208s" -ForegroundColor White
}

# Main execution
Show-TimeoutConfig
Check-RecentLogs
Show-Recommendations

Write-Host "`n🔍 To run this monitor continuously:" -ForegroundColor Cyan
Write-Host "  while (`$true) { Clear-Host; .\monitor-circuit-breakers.ps1; Start-Sleep 60 }" -ForegroundColor Gray
